"""
شاشة تسجيل الدخول
"""
import os
import sys
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QMessageBox, QFrame, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QIcon

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from controllers.auth_controller import AuthController

class LoginWindow(QWidget):
    """نافذة تسجيل الدخول"""
    
    # إشارة نجاح تسجيل الدخول
    login_success = pyqtSignal()
    
    def __init__(self, auth_controller: AuthController):
        """
        تهيئة نافذة تسجيل الدخول
        
        Args:
            auth_controller: متحكم المصادقة
        """
        super().__init__()
        
        self.auth_controller = auth_controller
        
        # إعداد النافذة
        self.setWindowTitle("تسجيل الدخول - نظام إدارة الحوادث المدرسية")
        self.setMinimumSize(400, 300)
        self.setObjectName("loginWidget")
        
        # إعداد أيقونة التطبيق
        icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "resources", "icons", "app.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # إنشاء التخطيط
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(50, 30, 50, 30)
        main_layout.setSpacing(20)
        
        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setFrameShape(QFrame.StyledPanel)
        login_frame.setObjectName("loginFrame")
        
        login_layout = QVBoxLayout(login_frame)
        login_layout.setContentsMargins(20, 20, 20, 20)
        login_layout.setSpacing(15)
        
        # عنوان تسجيل الدخول
        title_label = QLabel("نظام إدارة الحوادث المدرسية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("loginTitle")
        login_layout.addWidget(title_label)
        
        # شعار التطبيق
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        logo_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "resources", "icons", "logo.png")
        if os.path.exists(logo_path):
            logo_pixmap = QPixmap(logo_path)
            logo_label.setPixmap(logo_pixmap.scaled(128, 128, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        login_layout.addWidget(logo_label)
        
        # مساحة فارغة
        login_layout.addSpacing(10)
        
        # حقل اسم المستخدم
        username_layout = QHBoxLayout()
        username_label = QLabel("اسم المستخدم:")
        username_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.username_input = QLineEdit()
        self.username_input.setObjectName("usernameInput")
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        login_layout.addLayout(username_layout)
        
        # حقل كلمة المرور
        password_layout = QHBoxLayout()
        password_label = QLabel("كلمة المرور:")
        password_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.password_input = QLineEdit()
        self.password_input.setObjectName("passwordInput")
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        login_layout.addLayout(password_layout)
        
        # مساحة فارغة
        login_layout.addSpacing(10)
        
        # زر تسجيل الدخول
        login_button = QPushButton("تسجيل الدخول")
        login_button.setObjectName("loginButton")
        login_button.clicked.connect(self.login)
        login_layout.addWidget(login_button)
        
        # إضافة إطار تسجيل الدخول إلى التخطيط الرئيسي
        main_layout.addWidget(login_frame)
        
        # تعيين التخطيط الرئيسي
        self.setLayout(main_layout)
        
        # تعيين التركيز على حقل اسم المستخدم
        self.username_input.setFocus()
        
        # ربط مفتاح الإدخال بوظيفة تسجيل الدخول
        self.username_input.returnPressed.connect(self.login)
        self.password_input.returnPressed.connect(self.login)
    
    def login(self):
        """محاولة تسجيل الدخول"""
        
        # الحصول على اسم المستخدم وكلمة المرور
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        # التحقق من إدخال اسم المستخدم وكلمة المرور
        if not username:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال اسم المستخدم")
            self.username_input.setFocus()
            return
        
        if not password:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال كلمة المرور")
            self.password_input.setFocus()
            return
        
        # محاولة تسجيل الدخول
        if self.auth_controller.login(username, password):
            # نجاح تسجيل الدخول
            self.login_success.emit()
        else:
            # فشل تسجيل الدخول
            QMessageBox.critical(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_input.clear()
            self.password_input.setFocus()
