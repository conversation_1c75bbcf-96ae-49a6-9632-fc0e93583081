"""
شاشة تسجيل الدخول
"""
import os
import sys
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QMessageBox, QFrame, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QIcon

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from controllers.auth_controller import AuthController

class LoginWindow(QWidget):
    """نافذة تسجيل الدخول"""
    
    # إشارة نجاح تسجيل الدخول
    login_success = pyqtSignal()
    
    def __init__(self, auth_controller: AuthController):
        """
        تهيئة نافذة تسجيل الدخول
        
        Args:
            auth_controller: متحكم المصادقة
        """
        super().__init__()
        
        self.auth_controller = auth_controller
        
        # إعداد النافذة
        self.setWindowTitle("تسجيل الدخول - نظام إدارة الحوادث المدرسية")
        self.setMinimumSize(400, 300)
        self.setObjectName("loginWidget")
        
        # إعداد أيقونة التطبيق
        icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "resources", "icons", "app.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # إنشاء التخطيط
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(50, 30, 50, 30)
        main_layout.setSpacing(20)
        
        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setFrameShape(QFrame.StyledPanel)
        login_frame.setObjectName("loginFrame")
        
        login_layout = QVBoxLayout(login_frame)
        login_layout.setContentsMargins(20, 20, 20, 20)
        login_layout.setSpacing(15)
        
        # عنوان تسجيل الدخول
        title_label = QLabel("نظام إدارة الحوادث المدرسية")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("loginTitle")
        login_layout.addWidget(title_label)
        
        # شعار التطبيق
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        logo_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "resources", "icons", "logo.png")
        if os.path.exists(logo_path):
            logo_pixmap = QPixmap(logo_path)
            logo_label.setPixmap(logo_pixmap.scaled(128, 128, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        login_layout.addWidget(logo_label)
        
        # مساحة فارغة
        login_layout.addSpacing(10)
        
        # حقل اسم المستخدم
        username_layout = QHBoxLayout()
        username_label = QLabel("اسم المستخدم:")
        username_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.username_input = QLineEdit()
        self.username_input.setObjectName("usernameInput")
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        login_layout.addLayout(username_layout)
        
        # حقل كلمة المرور
        password_layout = QHBoxLayout()
        password_label = QLabel("كلمة المرور:")
        password_label.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        self.password_input = QLineEdit()
        self.password_input.setObjectName("passwordInput")
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        login_layout.addLayout(password_layout)
        
        # مساحة فارغة
        login_layout.addSpacing(10)
        
        # زر تسجيل الدخول
        login_button = QPushButton("تسجيل الدخول")
        login_button.setObjectName("loginButton")
        login_button.clicked.connect(self.login)
        login_layout.addWidget(login_button)
        
        # إضافة إطار تسجيل الدخول إلى التخطيط الرئيسي
        main_layout.addWidget(login_frame)
        
        # تعيين التخطيط الرئيسي
        self.setLayout(main_layout)
        
        # تعيين التركيز على حقل اسم المستخدم
        self.username_input.setFocus()
        
        # ربط مفتاح الإدخال بوظيفة تسجيل الدخول
        self.username_input.returnPressed.connect(self.login)
        self.password_input.returnPressed.connect(self.login)
    
    def login(self):
        """محاولة تسجيل الدخول"""
        
        # الحصول على اسم المستخدم وكلمة المرور
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        # التحقق من إدخال اسم المستخدم وكلمة المرور
        if not username:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال اسم المستخدم")
            self.username_input.setFocus()
            return
        
        if not password:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال كلمة المرور")
            self.password_input.setFocus()
            return
        
        # محاولة تسجيل الدخول
        if self.auth_controller.login(username, password):
            # نجاح تسجيل الدخول
            self.login_success.emit()
        else:
            # فشل تسجيل الدخول
            QMessageBox.critical(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_input.clear()
            self.password_input.setFocus()


class LoginWidget(QWidget):
    """ويدجت تسجيل الدخول للاستخدام داخل النافذة الرئيسية"""

    # إشارة نجاح تسجيل الدخول
    login_success = pyqtSignal()

    def __init__(self, auth_controller: AuthController):
        """
        تهيئة ويدجت تسجيل الدخول

        Args:
            auth_controller: متحكم المصادقة
        """
        super().__init__()

        self.auth_controller = auth_controller
        self.setObjectName("loginWidget")

        # إنشاء التخطيط
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(50, 50, 50, 50)
        main_layout.setSpacing(30)
        main_layout.setAlignment(Qt.AlignCenter)

        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setObjectName("loginFrame")
        login_frame.setMaximumWidth(400)
        login_frame.setMaximumHeight(500)
        login_frame.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        frame_layout = QVBoxLayout(login_frame)
        frame_layout.setContentsMargins(40, 40, 40, 40)
        frame_layout.setSpacing(25)

        # شعار التطبيق
        logo_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "resources", "icons", "logo.png")
        if os.path.exists(logo_path):
            logo_label = QLabel()
            logo_pixmap = QPixmap(logo_path)
            logo_label.setPixmap(logo_pixmap.scaled(80, 80, Qt.KeepAspectRatio, Qt.SmoothTransformation))
            logo_label.setAlignment(Qt.AlignCenter)
            frame_layout.addWidget(logo_label)

        # عنوان التطبيق
        title_label = QLabel("نظام إدارة الحوادث المدرسية")
        title_label.setObjectName("loginTitle")
        title_label.setAlignment(Qt.AlignCenter)
        frame_layout.addWidget(title_label)

        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        frame_layout.addWidget(username_label)

        self.username_input = QLineEdit()
        self.username_input.setObjectName("usernameInput")
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.returnPressed.connect(self.login)
        frame_layout.addWidget(self.username_input)

        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        frame_layout.addWidget(password_label)

        self.password_input = QLineEdit()
        self.password_input.setObjectName("passwordInput")
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.returnPressed.connect(self.login)
        frame_layout.addWidget(self.password_input)

        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.login)
        self.login_button.setDefault(True)
        frame_layout.addWidget(self.login_button)

        # معلومات افتراضية
        info_label = QLabel("المستخدم الافتراضي: admin | كلمة المرور: admin123")
        info_label.setObjectName("infoLabel")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #7f8c8d; font-size: 9pt;")
        frame_layout.addWidget(info_label)

        # إضافة الإطار إلى التخطيط الرئيسي
        main_layout.addWidget(login_frame, 0, Qt.AlignCenter)

        # تعيين التركيز على حقل اسم المستخدم
        self.username_input.setFocus()

    def login(self):
        """تسجيل الدخول"""

        # الحصول على البيانات
        username = self.username_input.text().strip()
        password = self.password_input.text()

        # التحقق من صحة البيانات
        if not username:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال اسم المستخدم")
            self.username_input.setFocus()
            return

        if not password:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال كلمة المرور")
            self.password_input.setFocus()
            return

        # تعطيل زر تسجيل الدخول أثناء المعالجة
        self.login_button.setEnabled(False)
        self.login_button.setText("جاري تسجيل الدخول...")

        # محاولة تسجيل الدخول
        if self.auth_controller.login(username, password):
            # نجح تسجيل الدخول
            self.login_success.emit()
        else:
            # فشل تسجيل الدخول
            QMessageBox.critical(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_input.clear()
            self.password_input.setFocus()

            # إعادة تفعيل زر تسجيل الدخول
            self.login_button.setEnabled(True)
            self.login_button.setText("تسجيل الدخول")
