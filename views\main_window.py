"""
النافذة الرئيسية للتطبيق
"""
import os
import sys
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QStackedWidget, QMessageBox, QAction, QToolBar, QStatusBar, QFrame,
    QSplitter, QTreeWidget, QTreeWidgetItem
)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QIcon

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import APP_NAME, APP_VERSION
from controllers.auth_controller import AuthController
from controllers.incident_controller import IncidentController
from controllers.report_controller import ReportController
from controllers.backup_controller import BackupController
from views.dashboard import DashboardWidget
from views.incident_form import IncidentFormWidget
from views.incident_list import IncidentListWidget
from views.reports import ReportsWidget
from views.settings import SettingsWidget
from views.user_management import UserManagementWidget
from views.login import LoginWidget

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(
        self,
        auth_controller: AuthController,
        incident_controller: IncidentController,
        report_controller: ReportController,
        backup_controller: BackupController
    ):
        """
        تهيئة النافذة الرئيسية
        
        Args:
            auth_controller: متحكم المصادقة
            incident_controller: متحكم الحوادث
            report_controller: متحكم التقارير
            backup_controller: متحكم النسخ الاحتياطي
        """
        super().__init__()
        
        self.auth_controller = auth_controller
        self.incident_controller = incident_controller
        self.report_controller = report_controller
        self.backup_controller = backup_controller

        # حالة تسجيل الدخول
        self.is_logged_in = False

        # إعداد النافذة
        self.setWindowTitle(f"{APP_NAME} - الإصدار {APP_VERSION}")
        self.setMinimumSize(1024, 768)

        # إعداد أيقونة التطبيق
        icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "resources", "icons", "app.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))

        # إنشاء التخطيط
        self.setup_ui()

        # عرض شاشة تسجيل الدخول أولاً
        self.show_login_screen()

        # التأكد من أن النافذة تظهر في المقدمة
        self.raise_()
        self.activateWindow()

    def show_login_screen(self):
        """عرض شاشة تسجيل الدخول"""
        self.main_stack.setCurrentWidget(self.login_widget)

        # إخفاء القوائم وشريط الأدوات
        if hasattr(self, 'menuBar'):
            self.menuBar().hide()
        if hasattr(self, 'toolbar'):
            self.toolbar.hide()
        if hasattr(self, 'statusBar'):
            self.statusBar().hide()

    def on_login_success(self):
        """معالجة نجاح تسجيل الدخول"""
        self.is_logged_in = True

        # إنشاء معلومات المستخدم
        self.setup_user_info()

        # إنشاء القوائم وشريط الأدوات
        self.create_menus()
        self.create_toolbar()
        self.create_status_bar()

        # عرض الواجهة الرئيسية
        self.main_stack.setCurrentWidget(self.main_interface)

        # عرض الصفحة الرئيسية
        self.show_dashboard()

    def setup_user_info(self):
        """إعداد معلومات المستخدم في الشريط الجانبي"""

        # مسح المحتوى السابق
        for i in reversed(range(self.user_layout.count())):
            self.user_layout.itemAt(i).widget().setParent(None)

        # الحصول على معلومات المستخدم
        user_info = self.auth_controller.get_current_user()
        if user_info:
            user_label = QLabel(f"مرحبًا، {user_info['full_name']}")
            user_label.setObjectName("userLabel")
            self.user_layout.addWidget(user_label)

            logout_button = QPushButton("تسجيل الخروج")
            logout_button.setObjectName("logoutButton")
            logout_button.clicked.connect(self.logout)
            self.user_layout.addWidget(logout_button)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""

        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # مكدس الصفحات (تسجيل الدخول والتطبيق الرئيسي)
        self.main_stack = QStackedWidget()
        main_layout.addWidget(self.main_stack)

        # إنشاء شاشة تسجيل الدخول
        self.login_widget = LoginWidget(self.auth_controller)
        self.login_widget.login_success.connect(self.on_login_success)
        self.main_stack.addWidget(self.login_widget)

        # إنشاء الواجهة الرئيسية
        self.main_interface = QWidget()
        self.setup_main_interface()
        self.main_stack.addWidget(self.main_interface)

    def setup_main_interface(self):
        """إعداد الواجهة الرئيسية للتطبيق"""

        # التخطيط الرئيسي للواجهة
        interface_layout = QHBoxLayout(self.main_interface)
        interface_layout.setContentsMargins(0, 0, 0, 0)
        interface_layout.setSpacing(0)

        # إنشاء المقسم
        splitter = QSplitter(Qt.Horizontal)
        splitter.setHandleWidth(1)
        interface_layout.addWidget(splitter)
        
        # الشريط الجانبي
        sidebar_widget = QWidget()
        sidebar_widget.setMinimumWidth(200)
        sidebar_widget.setMaximumWidth(300)
        sidebar_layout = QVBoxLayout(sidebar_widget)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        
        # شعار التطبيق
        logo_frame = QFrame()
        logo_frame.setObjectName("logoFrame")
        logo_layout = QVBoxLayout(logo_frame)
        logo_layout.setContentsMargins(10, 10, 10, 10)
        
        logo_label = QLabel(APP_NAME)
        logo_label.setObjectName("logoLabel")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_layout.addWidget(logo_label)
        
        sidebar_layout.addWidget(logo_frame)
        
        # شجرة القائمة
        self.menu_tree = QTreeWidget()
        self.menu_tree.setHeaderHidden(True)
        self.menu_tree.setObjectName("menuTree")
        self.menu_tree.setIconSize(QSize(24, 24))
        self.menu_tree.setIndentation(10)
        
        # إضافة عناصر القائمة
        self.add_menu_items()
        
        # ربط حدث النقر على عناصر القائمة
        self.menu_tree.itemClicked.connect(self.on_menu_item_clicked)
        
        sidebar_layout.addWidget(self.menu_tree)
        
        # معلومات المستخدم (سيتم إنشاؤها بعد تسجيل الدخول)
        self.user_frame = QFrame()
        self.user_frame.setObjectName("userFrame")
        self.user_layout = QVBoxLayout(self.user_frame)
        self.user_layout.setContentsMargins(10, 10, 10, 10)

        sidebar_layout.addWidget(self.user_frame)
        
        # إضافة الشريط الجانبي إلى المقسم
        splitter.addWidget(sidebar_widget)
        
        # منطقة المحتوى
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        
        # عنوان الصفحة
        self.title_frame = QFrame()
        self.title_frame.setObjectName("titleFrame")
        title_layout = QHBoxLayout(self.title_frame)
        title_layout.setContentsMargins(20, 10, 20, 10)
        
        self.title_label = QLabel()
        self.title_label.setObjectName("titleLabel")
        title_layout.addWidget(self.title_label)
        
        content_layout.addWidget(self.title_frame)
        
        # مكدس الصفحات
        self.content_stack = QStackedWidget()
        content_layout.addWidget(self.content_stack)
        
        # إضافة منطقة المحتوى إلى المقسم
        splitter.addWidget(content_widget)
        
        # تعيين نسب المقسم
        splitter.setSizes([200, 800])
    
    def add_menu_items(self):
        """إضافة عناصر القائمة"""
        
        # الرئيسية
        dashboard_item = QTreeWidgetItem(self.menu_tree)
        dashboard_item.setText(0, "الرئيسية")
        dashboard_item.setData(0, Qt.UserRole, "dashboard")
        
        # الحوادث
        incidents_item = QTreeWidgetItem(self.menu_tree)
        incidents_item.setText(0, "الحوادث")
        incidents_item.setData(0, Qt.UserRole, "incidents")
        
        # حادثة جديدة
        new_incident_item = QTreeWidgetItem(incidents_item)
        new_incident_item.setText(0, "حادثة جديدة")
        new_incident_item.setData(0, Qt.UserRole, "new_incident")
        
        # قائمة الحوادث
        incident_list_item = QTreeWidgetItem(incidents_item)
        incident_list_item.setText(0, "قائمة الحوادث")
        incident_list_item.setData(0, Qt.UserRole, "incident_list")
        
        # التقارير
        reports_item = QTreeWidgetItem(self.menu_tree)
        reports_item.setText(0, "التقارير")
        reports_item.setData(0, Qt.UserRole, "reports")
        
        # الإعدادات
        settings_item = QTreeWidgetItem(self.menu_tree)
        settings_item.setText(0, "الإعدادات")
        settings_item.setData(0, Qt.UserRole, "settings")
        
        # إدارة المستخدمين (للمدير فقط)
        if self.auth_controller.is_admin():
            user_management_item = QTreeWidgetItem(settings_item)
            user_management_item.setText(0, "إدارة المستخدمين")
            user_management_item.setData(0, Qt.UserRole, "user_management")
        
        # توسيع جميع العناصر
        self.menu_tree.expandAll()
    
    def on_menu_item_clicked(self, item, column):
        """
        معالجة النقر على عناصر القائمة
        
        Args:
            item: العنصر الذي تم النقر عليه
            column: رقم العمود
        """
        # الحصول على معرف العنصر
        item_id = item.data(0, Qt.UserRole)
        
        # تنفيذ الإجراء المناسب
        if item_id == "dashboard":
            self.show_dashboard()
        elif item_id == "new_incident":
            self.show_new_incident()
        elif item_id == "incident_list":
            self.show_incident_list()
        elif item_id == "reports":
            self.show_reports()
        elif item_id == "settings":
            self.show_settings()
        elif item_id == "user_management":
            self.show_user_management()
    
    def create_menus(self):
        """إنشاء القوائم"""
        
        # القائمة الرئيسية
        main_menu = self.menuBar()
        
        # قائمة الملف
        file_menu = main_menu.addMenu("ملف")
        
        # تسجيل الخروج
        logout_action = QAction("تسجيل الخروج", self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        # خروج
        exit_action = QAction("خروج", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة الحوادث
        incidents_menu = main_menu.addMenu("الحوادث")
        
        # حادثة جديدة
        new_incident_action = QAction("حادثة جديدة", self)
        new_incident_action.triggered.connect(self.show_new_incident)
        incidents_menu.addAction(new_incident_action)
        
        # قائمة الحوادث
        incident_list_action = QAction("قائمة الحوادث", self)
        incident_list_action.triggered.connect(self.show_incident_list)
        incidents_menu.addAction(incident_list_action)
        
        # قائمة التقارير
        reports_menu = main_menu.addMenu("التقارير")
        
        # تقرير يومي
        daily_report_action = QAction("تقرير يومي", self)
        daily_report_action.triggered.connect(lambda: self.show_reports("daily"))
        reports_menu.addAction(daily_report_action)
        
        # تقرير أسبوعي
        weekly_report_action = QAction("تقرير أسبوعي", self)
        weekly_report_action.triggered.connect(lambda: self.show_reports("weekly"))
        reports_menu.addAction(weekly_report_action)
        
        # تقرير شهري
        monthly_report_action = QAction("تقرير شهري", self)
        monthly_report_action.triggered.connect(lambda: self.show_reports("monthly"))
        reports_menu.addAction(monthly_report_action)
        
        # تقرير سنوي
        yearly_report_action = QAction("تقرير سنوي", self)
        yearly_report_action.triggered.connect(lambda: self.show_reports("yearly"))
        reports_menu.addAction(yearly_report_action)
        
        # قائمة الإعدادات
        settings_menu = main_menu.addMenu("الإعدادات")
        
        # إعدادات التطبيق
        app_settings_action = QAction("إعدادات التطبيق", self)
        app_settings_action.triggered.connect(self.show_settings)
        settings_menu.addAction(app_settings_action)
        
        # إدارة المستخدمين (للمدير فقط)
        if self.auth_controller.is_admin():
            user_management_action = QAction("إدارة المستخدمين", self)
            user_management_action.triggered.connect(self.show_user_management)
            settings_menu.addAction(user_management_action)
        
        # قائمة المساعدة
        help_menu = main_menu.addMenu("مساعدة")
        
        # دليل المستخدم
        user_guide_action = QAction("دليل المستخدم", self)
        user_guide_action.triggered.connect(self.show_user_guide)
        help_menu.addAction(user_guide_action)
        
        # حول
        about_action = QAction("حول", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        
        # شريط الأدوات الرئيسي
        main_toolbar = QToolBar("شريط الأدوات الرئيسي", self)
        main_toolbar.setObjectName("mainToolbar")
        main_toolbar.setIconSize(QSize(24, 24))
        self.addToolBar(main_toolbar)
        
        # الرئيسية
        dashboard_action = QAction("الرئيسية", self)
        dashboard_action.triggered.connect(self.show_dashboard)
        main_toolbar.addAction(dashboard_action)
        
        # حادثة جديدة
        new_incident_action = QAction("حادثة جديدة", self)
        new_incident_action.triggered.connect(self.show_new_incident)
        main_toolbar.addAction(new_incident_action)
        
        # قائمة الحوادث
        incident_list_action = QAction("قائمة الحوادث", self)
        incident_list_action.triggered.connect(self.show_incident_list)
        main_toolbar.addAction(incident_list_action)
        
        # التقارير
        reports_action = QAction("التقارير", self)
        reports_action.triggered.connect(self.show_reports)
        main_toolbar.addAction(reports_action)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""

        # شريط الحالة
        status_bar = QStatusBar(self)
        self.setStatusBar(status_bar)

        # معلومات المستخدم
        user_info = self.auth_controller.get_current_user()
        if user_info:
            user_label = QLabel(f"المستخدم: {user_info['username']} | الدور: {user_info['role']}")
            status_bar.addPermanentWidget(user_label)

        # إصدار التطبيق
        version_label = QLabel(f"الإصدار: {APP_VERSION}")
        status_bar.addPermanentWidget(version_label)
    
    def show_dashboard(self):
        """عرض الصفحة الرئيسية"""
        
        # تعيين العنوان
        self.title_label.setText("الرئيسية")
        
        # البحث عن الويدجت في المكدس
        for i in range(self.content_stack.count()):
            if isinstance(self.content_stack.widget(i), DashboardWidget):
                self.content_stack.setCurrentIndex(i)
                return
        
        # إنشاء الويدجت إذا لم يكن موجودًا
        dashboard_widget = DashboardWidget(self.incident_controller)
        self.content_stack.addWidget(dashboard_widget)
        self.content_stack.setCurrentWidget(dashboard_widget)
    
    def show_new_incident(self):
        """عرض صفحة إضافة حادثة جديدة"""
        
        # تعيين العنوان
        self.title_label.setText("إضافة حادثة جديدة")
        
        # إنشاء الويدجت
        incident_form_widget = IncidentFormWidget(self.incident_controller)
        
        # ربط إشارة الحفظ
        incident_form_widget.incident_saved.connect(self.on_incident_saved)
        
        # إضافة الويدجت إلى المكدس
        self.content_stack.addWidget(incident_form_widget)
        self.content_stack.setCurrentWidget(incident_form_widget)
    
    def show_incident_list(self):
        """عرض صفحة قائمة الحوادث"""
        
        # تعيين العنوان
        self.title_label.setText("قائمة الحوادث")
        
        # البحث عن الويدجت في المكدس
        for i in range(self.content_stack.count()):
            if isinstance(self.content_stack.widget(i), IncidentListWidget):
                # تحديث القائمة
                self.content_stack.widget(i).refresh_list()
                self.content_stack.setCurrentIndex(i)
                return
        
        # إنشاء الويدجت إذا لم يكن موجودًا
        incident_list_widget = IncidentListWidget(self.incident_controller)
        
        # ربط إشارة تعديل الحادثة
        incident_list_widget.edit_incident.connect(self.on_edit_incident)
        
        # إضافة الويدجت إلى المكدس
        self.content_stack.addWidget(incident_list_widget)
        self.content_stack.setCurrentWidget(incident_list_widget)
    
    def show_reports(self, report_type=None):
        """
        عرض صفحة التقارير
        
        Args:
            report_type: نوع التقرير (اختياري)
        """
        # تعيين العنوان
        self.title_label.setText("التقارير")
        
        # البحث عن الويدجت في المكدس
        for i in range(self.content_stack.count()):
            if isinstance(self.content_stack.widget(i), ReportsWidget):
                # تعيين نوع التقرير إذا تم توفيره
                if report_type:
                    self.content_stack.widget(i).set_report_type(report_type)
                
                self.content_stack.setCurrentIndex(i)
                return
        
        # إنشاء الويدجت إذا لم يكن موجودًا
        reports_widget = ReportsWidget(self.report_controller)
        
        # تعيين نوع التقرير إذا تم توفيره
        if report_type:
            reports_widget.set_report_type(report_type)
        
        # إضافة الويدجت إلى المكدس
        self.content_stack.addWidget(reports_widget)
        self.content_stack.setCurrentWidget(reports_widget)
    
    def show_settings(self):
        """عرض صفحة الإعدادات"""
        
        # تعيين العنوان
        self.title_label.setText("الإعدادات")
        
        # البحث عن الويدجت في المكدس
        for i in range(self.content_stack.count()):
            if isinstance(self.content_stack.widget(i), SettingsWidget):
                self.content_stack.setCurrentIndex(i)
                return
        
        # إنشاء الويدجت إذا لم يكن موجودًا
        settings_widget = SettingsWidget(
            self.auth_controller,
            self.backup_controller
        )
        
        # إضافة الويدجت إلى المكدس
        self.content_stack.addWidget(settings_widget)
        self.content_stack.setCurrentWidget(settings_widget)
    
    def show_user_management(self):
        """عرض صفحة إدارة المستخدمين"""
        
        # التحقق من صلاحيات المدير
        if not self.auth_controller.is_admin():
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية الوصول إلى هذه الصفحة")
            return
        
        # تعيين العنوان
        self.title_label.setText("إدارة المستخدمين")
        
        # البحث عن الويدجت في المكدس
        for i in range(self.content_stack.count()):
            if isinstance(self.content_stack.widget(i), UserManagementWidget):
                # تحديث قائمة المستخدمين
                self.content_stack.widget(i).refresh_users()
                self.content_stack.setCurrentIndex(i)
                return
        
        # إنشاء الويدجت إذا لم يكن موجودًا
        user_management_widget = UserManagementWidget(self.auth_controller)
        
        # إضافة الويدجت إلى المكدس
        self.content_stack.addWidget(user_management_widget)
        self.content_stack.setCurrentWidget(user_management_widget)
    
    def show_user_guide(self):
        """عرض دليل المستخدم"""
        
        # مسار دليل المستخدم
        user_guide_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "resources", "user_guide.pdf")
        
        # التحقق من وجود الملف
        if os.path.exists(user_guide_path):
            # فتح الملف باستخدام التطبيق الافتراضي
            import subprocess
            if sys.platform == "win32":
                os.startfile(user_guide_path)
            elif sys.platform == "darwin":
                subprocess.call(["open", user_guide_path])
            else:
                subprocess.call(["xdg-open", user_guide_path])
        else:
            QMessageBox.information(self, "معلومات", "دليل المستخدم غير متوفر حاليًا")
    
    def show_about(self):
        """عرض معلومات حول التطبيق"""
        
        QMessageBox.about(
            self,
            "حول التطبيق",
            f"<h2>{APP_NAME}</h2>"
            f"<p>الإصدار: {APP_VERSION}</p>"
            "<p>تطبيق سطح مكتب لإدارة الحوادث المدرسية والضمان المدرسي</p>"
            "<p>جميع الحقوق محفوظة © 2023</p>"
        )
    
    def on_incident_saved(self):
        """معالجة حدث حفظ الحادثة"""
        
        # عرض رسالة نجاح
        QMessageBox.information(self, "معلومات", "تم حفظ الحادثة بنجاح")
        
        # الانتقال إلى قائمة الحوادث
        self.show_incident_list()
    
    def on_edit_incident(self, incident_id):
        """
        معالجة حدث تعديل الحادثة
        
        Args:
            incident_id: معرف الحادثة
        """
        # تعيين العنوان
        self.title_label.setText("تعديل الحادثة")
        
        # إنشاء الويدجت
        incident_form_widget = IncidentFormWidget(self.incident_controller, incident_id)
        
        # ربط إشارة الحفظ
        incident_form_widget.incident_saved.connect(self.on_incident_saved)
        
        # إضافة الويدجت إلى المكدس
        self.content_stack.addWidget(incident_form_widget)
        self.content_stack.setCurrentWidget(incident_form_widget)
    
    def logout(self):
        """تسجيل الخروج"""

        # التأكيد قبل تسجيل الخروج
        reply = QMessageBox.question(
            self,
            "تأكيد",
            "هل أنت متأكد من رغبتك في تسجيل الخروج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # تسجيل الخروج
            self.auth_controller.logout()
            self.is_logged_in = False

            # مسح حقول تسجيل الدخول
            self.login_widget.username_input.clear()
            self.login_widget.password_input.clear()
            self.login_widget.username_input.setFocus()

            # العودة إلى شاشة تسجيل الدخول
            self.show_login_screen()
    
    def closeEvent(self, event):
        """
        معالجة حدث إغلاق النافذة
        
        Args:
            event: حدث الإغلاق
        """
        # التأكيد قبل الإغلاق
        reply = QMessageBox.question(
            self,
            "تأكيد",
            "هل أنت متأكد من رغبتك في إغلاق التطبيق؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # قبول الحدث (إغلاق التطبيق)
            event.accept()
        else:
            # رفض الحدث (إلغاء الإغلاق)
            event.ignore()
