#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ملف إنشاء حزمة التثبيت للتطبيق
"""

import sys
from cx_Freeze import setup, Executable
import os

# المسار الرئيسي للمشروع
base_dir = os.path.dirname(os.path.abspath(__file__))

# استيراد معلومات التطبيق
sys.path.append(base_dir)
from config import APP_NAME, APP_VERSION, APP_AUTHOR

# تحديد نظام التشغيل
base = None
if sys.platform == "win32":
    base = "Win32GUI"

# الملفات المطلوبة
include_files = [
    (os.path.join(base_dir, "resources"), "resources"),
    (os.path.join(base_dir, "data"), "data")
]

# المكتبات المطلوبة
packages = [
    "PyQt5",
    "sqlalchemy",
    "bcrypt",
    "openpyxl",
    "reportlab",
    "alembic",
    "pydrive2"
]

# خيارات البناء
build_options = {
    "packages": packages,
    "excludes": [],
    "include_files": include_files,
    "include_msvcr": True,
    "optimize": 2
}

# خيارات ملف MSI
bdist_msi_options = {
    "upgrade_code": "{12345678-1234-5678-abcd-1234567890ab}",
    "add_to_path": False,
    "initial_target_dir": f"[ProgramFilesFolder]\\{APP_AUTHOR}\\{APP_NAME}"
}

# إنشاء الملف التنفيذي
executables = [
    Executable(
        script="main.py",
        base=base,
        target_name=f"{APP_NAME}.exe",
        icon="resources/icons/app.ico",
        shortcut_name=APP_NAME,
        shortcut_dir="DesktopFolder",
        copyright=f"Copyright (c) 2023 {APP_AUTHOR}"
    )
]

# إعداد الحزمة
setup(
    name=APP_NAME,
    version=APP_VERSION,
    description="نظام إدارة الحوادث المدرسية",
    author=APP_AUTHOR,
    options={
        "build_exe": build_options,
        "bdist_msi": bdist_msi_options
    },
    executables=executables
)
