"""
متحكم المصادقة - يدير عمليات تسجيل الدخول والخروج وإدارة الجلسات
"""
import os
import sys
from typing import Optional, Dict, Any

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import User, UserRole
from models.user import UserManager

class AuthController:
    """متحكم المصادقة"""

    def __init__(self):
        """تهيئة متحكم المصادقة"""
        self.current_user = None
        self.current_user_data = None

    def login(self, username: str, password: str) -> bool:
        """
        تسجيل الدخول

        Args:
            username: اسم المستخدم
            password: كلمة المرور

        Returns:
            True إذا نجحت عملية تسجيل الدخول، False خلاف ذلك
        """
        user_data = UserManager.authenticate(username, password)
        if user_data:
            # حفظ معلومات المستخدم
            self.current_user_data = user_data
            self.current_user = None  # لم نعد نحتاج لحفظ كائن المستخدم
            return True
        return False
    
    def logout(self) -> None:
        """تسجيل الخروج"""
        self.current_user = None
        self.current_user_data = None

    def is_authenticated(self) -> bool:
        """
        التحقق مما إذا كان المستخدم مسجل الدخول

        Returns:
            True إذا كان المستخدم مسجل الدخول، False خلاف ذلك
        """
        return self.current_user_data is not None

    def is_admin(self) -> bool:
        """
        التحقق مما إذا كان المستخدم الحالي مديرًا

        Returns:
            True إذا كان المستخدم مديرًا، False خلاف ذلك
        """
        if not self.is_authenticated():
            return False

        return self.current_user_data['role'] == UserRole.ADMIN
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """
        الحصول على معلومات المستخدم الحالي

        Returns:
            معلومات المستخدم إذا كان مسجل الدخول، None خلاف ذلك
        """
        if not self.is_authenticated():
            return None

        return {
            'id': self.current_user_data['id'],
            'username': self.current_user_data['username'],
            'full_name': self.current_user_data['full_name'],
            'email': self.current_user_data['email'],
            'role': self.current_user_data['role'].value,
            'is_active': self.current_user_data['is_active'],
            'last_login': self.current_user_data['last_login']
        }
    
    def change_password(self, current_password: str, new_password: str) -> bool:
        """
        تغيير كلمة مرور المستخدم الحالي

        Args:
            current_password: كلمة المرور الحالية
            new_password: كلمة المرور الجديدة

        Returns:
            True إذا نجحت العملية، False خلاف ذلك
        """
        if not self.is_authenticated():
            return False

        return UserManager.change_password(self.current_user_data['id'], current_password, new_password)
    
    def create_user(self, username: str, password: str, full_name: str, email: str = None, role: UserRole = UserRole.USER) -> bool:
        """
        إنشاء مستخدم جديد (يتطلب صلاحيات المدير)
        
        Args:
            username: اسم المستخدم
            password: كلمة المرور
            full_name: الاسم الكامل
            email: البريد الإلكتروني (اختياري)
            role: دور المستخدم
            
        Returns:
            True إذا نجحت العملية، False خلاف ذلك
        """
        if not self.is_admin():
            return False
        
        user = UserManager.create_user(username, password, full_name, email, role)
        return user is not None
    
    def update_user(self, user_id: int, full_name: str = None, email: str = None, is_active: bool = None, role: UserRole = None) -> bool:
        """
        تحديث بيانات المستخدم (يتطلب صلاحيات المدير)
        
        Args:
            user_id: معرف المستخدم
            full_name: الاسم الكامل (اختياري)
            email: البريد الإلكتروني (اختياري)
            is_active: حالة المستخدم (اختياري)
            role: دور المستخدم (اختياري)
            
        Returns:
            True إذا نجحت العملية، False خلاف ذلك
        """
        if not self.is_admin():
            return False
        
        return UserManager.update_user(user_id, full_name, email, is_active, role)
    
    def get_users(self) -> list:
        """
        الحصول على قائمة المستخدمين (يتطلب صلاحيات المدير)
        
        Returns:
            قائمة بمعلومات المستخدمين إذا كان المستخدم الحالي مديرًا، قائمة فارغة خلاف ذلك
        """
        if not self.is_admin():
            return []
        
        return UserManager.get_users()

    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        الحصول على معلومات المستخدم بواسطة المعرف (يتطلب صلاحيات المدير)

        Args:
            user_id: معرف المستخدم

        Returns:
            معلومات المستخدم إذا وجد، None خلاف ذلك
        """
        if not self.is_admin():
            return None

        return UserManager.get_user_by_id(user_id)

    def get_user_activities(self, user_id: int = None) -> list:
        """
        الحصول على سجل نشاطات المستخدم
        
        Args:
            user_id: معرف المستخدم (اختياري، إذا لم يتم توفيره، يتم استخدام المستخدم الحالي)
            
        Returns:
            قائمة بنشاطات المستخدم
        """
        if not self.is_authenticated():
            return []
        
        # إذا كان المستخدم مديرًا، يمكنه الوصول إلى نشاطات أي مستخدم
        if self.is_admin() and user_id is not None:
            return UserManager.get_user_activities(user_id)
        
        # المستخدم العادي يمكنه الوصول إلى نشاطاته فقط
        return UserManager.get_user_activities(self.current_user_data['id'])
