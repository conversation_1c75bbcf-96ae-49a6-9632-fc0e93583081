#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام إدارة الحوادث المدرسية
تطبيق سطح مكتب لإدارة الحوادث المدرسية والضمان المدرسي
"""

import sys
import os
import traceback
from PyQt5.QtWidgets import QApplication, QSplashScreen, QMessageBox
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QIcon

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الوحدات الخاصة بالتطبيق
from config import APP_NAME, APP_VERSION
from models.database import init_db
from controllers.auth_controller import AuthController
from controllers.incident_controller import IncidentController
from controllers.report_controller import ReportController
from controllers.backup_controller import BackupController
from views.login import LoginWindow
from views.main_window import MainWindow

def setup_exception_handling():
    """إعداد معالجة الاستثناءات غير المتوقعة"""
    
    def show_exception_box(exctype, value, tb):
        """عرض مربع حوار للاستثناءات غير المتوقعة"""
        traceback_details = ''.join(traceback.format_exception(exctype, value, tb))
        
        error_box = QMessageBox()
        error_box.setWindowTitle("خطأ غير متوقع")
        error_box.setIcon(QMessageBox.Critical)
        error_box.setText("حدث خطأ غير متوقع في التطبيق.")
        error_box.setInformativeText("يرجى الاتصال بالدعم الفني وتقديم تفاصيل الخطأ التالية:")
        error_box.setDetailedText(traceback_details)
        error_box.setStandardButtons(QMessageBox.Ok)
        error_box.exec_()
        
        # تسجيل الخطأ في ملف
        log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", "logs")
        os.makedirs(log_dir, exist_ok=True)
        
        import datetime
        log_file = os.path.join(log_dir, f"error_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"تاريخ الخطأ: {datetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')}\n")
            f.write(f"نوع الخطأ: {exctype.__name__}\n")
            f.write(f"رسالة الخطأ: {value}\n")
            f.write("\nتفاصيل الخطأ:\n")
            f.write(traceback_details)
    
    # تعيين معالج الاستثناءات
    sys.excepthook = show_exception_box

def main():
    """الدالة الرئيسية للتطبيق"""
    
    # إعداد معالجة الاستثناءات
    setup_exception_handling()
    
    # إنشاء تطبيق PyQt
    app = QApplication(sys.argv)
    app.setApplicationName(APP_NAME)
    app.setApplicationVersion(APP_VERSION)
    
    # تعيين اتجاه النص من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تحميل أنماط CSS
    style_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "resources", "styles.qss")
    if os.path.exists(style_path):
        with open(style_path, 'r', encoding='utf-8') as f:
            app.setStyleSheet(f.read())
    
    # إنشاء شاشة البداية
    splash_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "resources", "icons", "splash.png")
    if os.path.exists(splash_path):
        splash_pixmap = QPixmap(splash_path)
        splash = QSplashScreen(splash_pixmap)
        splash.show()
        app.processEvents()
    else:
        splash = None
    
    # تهيئة قاعدة البيانات
    try:
        init_db()
    except Exception as e:
        if splash:
            splash.close()
        
        error_box = QMessageBox()
        error_box.setWindowTitle("خطأ في قاعدة البيانات")
        error_box.setIcon(QMessageBox.Critical)
        error_box.setText("حدث خطأ أثناء تهيئة قاعدة البيانات.")
        error_box.setInformativeText(str(e))
        error_box.setStandardButtons(QMessageBox.Ok)
        error_box.exec_()
        
        return 1
    
    # إنشاء المتحكمات
    auth_controller = AuthController()
    incident_controller = IncidentController(auth_controller)
    report_controller = ReportController(auth_controller)
    backup_controller = BackupController(auth_controller)
    
    # إنشاء نافذة تسجيل الدخول
    login_window = LoginWindow(auth_controller)
    
    # إغلاق شاشة البداية بعد تحميل التطبيق
    if splash:
        QTimer.singleShot(1500, splash.close)
    
    # عرض نافذة تسجيل الدخول
    login_window.show()
    
    # إنشاء النافذة الرئيسية عند نجاح تسجيل الدخول
    def on_login_success():
        main_window = MainWindow(
            auth_controller=auth_controller,
            incident_controller=incident_controller,
            report_controller=report_controller,
            backup_controller=backup_controller
        )
        main_window.show()
        login_window.close()
    
    # ربط إشارة نجاح تسجيل الدخول
    login_window.login_success.connect(on_login_success)
    
    # تشغيل حلقة الأحداث
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
