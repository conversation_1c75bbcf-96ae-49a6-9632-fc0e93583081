"""
نموذج إدخال الحادثة
"""
import os
import sys
import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QComboBox,
    QDateEdit, QTextEdit, QPushButton, QFormLayout, QGroupBox, QMessageBox,
    QScrollArea, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal, QDate

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from controllers.incident_controller import IncidentController

class IncidentFormWidget(QWidget):
    """ويدجت نموذج إدخال الحادثة"""
    
    # إشارة حفظ الحادثة
    incident_saved = pyqtSignal()
    
    def __init__(self, incident_controller: IncidentController, incident_id: int = None):
        """
        تهيئة ويدجت نموذج إدخال الحادثة
        
        Args:
            incident_controller: متحكم الحوادث
            incident_id: معرف الحادثة (اختياري، للتعديل)
        """
        super().__init__()
        
        self.incident_controller = incident_controller
        self.incident_id = incident_id
        self.incident_data = None
        
        # إذا تم توفير معرف الحادثة، فهذا يعني أننا في وضع التعديل
        if incident_id:
            self.incident_data = self.incident_controller.get_incident(incident_id)
        
        # إنشاء التخطيط
        self.setup_ui()
        
        # ملء النموذج بالبيانات إذا كنا في وضع التعديل
        if self.incident_data:
            self.fill_form()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # ويدجت المحتوى
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)
        
        # مجموعة معلومات التلميذ
        student_group = QGroupBox("معلومات التلميذ")
        student_layout = QFormLayout()
        
        # اسم التلميذ
        self.student_name_input = QLineEdit()
        student_layout.addRow("اسم التلميذ:", self.student_name_input)
        
        # رقم مسار
        self.massar_number_input = QLineEdit()
        student_layout.addRow("رقم مسار:", self.massar_number_input)
        
        # الجنس
        self.gender_input = QComboBox()
        self.gender_input.addItems(["ذكر", "أنثى"])
        student_layout.addRow("الجنس:", self.gender_input)
        
        student_group.setLayout(student_layout)
        content_layout.addWidget(student_group)
        
        # مجموعة معلومات المؤسسة
        institution_group = QGroupBox("معلومات المؤسسة")
        institution_layout = QFormLayout()
        
        # المؤسسة
        self.institution_input = QLineEdit()
        institution_layout.addRow("المؤسسة:", self.institution_input)
        
        # الوسط
        self.environment_input = QComboBox()
        self.environment_input.addItems(["حضري", "قروي"])
        institution_layout.addRow("الوسط:", self.environment_input)
        
        # السلك
        self.education_level_input = QComboBox()
        self.education_level_input.addItems(["ابتدائي", "إعدادي", "تأهيلي"])
        institution_layout.addRow("السلك:", self.education_level_input)
        
        institution_group.setLayout(institution_layout)
        content_layout.addWidget(institution_group)
        
        # مجموعة معلومات الحادثة
        incident_group = QGroupBox("معلومات الحادثة")
        incident_layout = QFormLayout()
        
        # رقم الملف
        self.file_number_input = QLineEdit()
        self.file_number_input.setPlaceholderText("سيتم إنشاؤه تلقائيًا")
        self.file_number_input.setReadOnly(True)
        incident_layout.addRow("رقم الملف:", self.file_number_input)
        
        # نوع الحادثة
        self.incident_type_input = QComboBox()
        self.incident_type_input.addItems(["حادثة مدرسية", "حادثة تنقل", "حادثة رياضية"])
        incident_layout.addRow("نوع الحادثة:", self.incident_type_input)
        
        # مرجع التأمين
        self.insurance_reference_input = QLineEdit()
        incident_layout.addRow("مرجع التأمين:", self.insurance_reference_input)
        
        # تاريخ التسوية
        self.settlement_date_input = QDateEdit()
        self.settlement_date_input.setCalendarPopup(True)
        self.settlement_date_input.setDate(QDate.currentDate())
        self.settlement_date_input.setDisplayFormat("yyyy/MM/dd")
        incident_layout.addRow("تاريخ التسوية:", self.settlement_date_input)
        
        # الحالة
        self.status_input = QComboBox()
        self.status_input.addItems(["قيد التسوية", "تمت التسوية", "مرفوض", "تم الدفع"])
        incident_layout.addRow("الحالة:", self.status_input)
        
        # ملاحظات
        self.notes_input = QTextEdit()
        incident_layout.addRow("ملاحظات:", self.notes_input)
        
        incident_group.setLayout(incident_layout)
        content_layout.addWidget(incident_group)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(10)
        
        # زر الحفظ
        self.save_button = QPushButton("حفظ")
        self.save_button.clicked.connect(self.save_incident)
        actions_layout.addWidget(self.save_button)
        
        # زر الإلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.cancel)
        actions_layout.addWidget(self.cancel_button)
        
        content_layout.addLayout(actions_layout)
        
        # تعيين ويدجت المحتوى لمنطقة التمرير
        scroll_area.setWidget(content_widget)
        
        # إضافة منطقة التمرير إلى التخطيط الرئيسي
        main_layout.addWidget(scroll_area)
    
    def fill_form(self):
        """ملء النموذج بالبيانات الحالية للحادثة"""
        
        if not self.incident_data:
            return
        
        # ملء معلومات التلميذ
        self.student_name_input.setText(self.incident_data.get('student_name', ''))
        self.massar_number_input.setText(self.incident_data.get('massar_number', ''))
        self.gender_input.setCurrentText(self.incident_data.get('gender', 'ذكر'))
        
        # ملء معلومات المؤسسة
        self.institution_input.setText(self.incident_data.get('institution', ''))
        self.environment_input.setCurrentText(self.incident_data.get('environment', 'حضري'))
        self.education_level_input.setCurrentText(self.incident_data.get('education_level', 'ابتدائي'))
        
        # ملء معلومات الحادثة
        self.file_number_input.setText(self.incident_data.get('file_number', ''))
        self.incident_type_input.setCurrentText(self.incident_data.get('incident_type', 'حادثة مدرسية'))
        self.insurance_reference_input.setText(self.incident_data.get('insurance_reference', ''))
        
        # تعيين تاريخ التسوية
        settlement_date = self.incident_data.get('settlement_date')
        if settlement_date:
            if isinstance(settlement_date, str):
                try:
                    date = QDate.fromString(settlement_date, "yyyy/MM/dd")
                    self.settlement_date_input.setDate(date)
                except:
                    pass
            elif isinstance(settlement_date, datetime.datetime):
                date = QDate(settlement_date.year, settlement_date.month, settlement_date.day)
                self.settlement_date_input.setDate(date)
        
        # تعيين الحالة
        self.status_input.setCurrentText(self.incident_data.get('status', 'قيد التسوية'))
        
        # تعيين الملاحظات
        self.notes_input.setText(self.incident_data.get('notes', ''))
    
    def save_incident(self):
        """حفظ الحادثة"""
        
        # التحقق من الحقول المطلوبة
        if not self.validate_form():
            return
        
        # جمع البيانات من النموذج
        student_name = self.student_name_input.text().strip()
        massar_number = self.massar_number_input.text().strip()
        gender = self.gender_input.currentText()
        institution = self.institution_input.text().strip()
        environment = self.environment_input.currentText()
        education_level = self.education_level_input.currentText()
        file_number = self.file_number_input.text().strip()
        incident_type = self.incident_type_input.currentText()
        insurance_reference = self.insurance_reference_input.text().strip()
        settlement_date = self.settlement_date_input.date().toString("yyyy/MM/dd")
        status = self.status_input.currentText()
        notes = self.notes_input.toPlainText().strip()
        
        # إذا كنا في وضع التعديل
        if self.incident_id:
            # تحديث الحادثة
            success = self.incident_controller.update_incident(
                incident_id=self.incident_id,
                student_name=student_name,
                massar_number=massar_number,
                gender=gender,
                institution=institution,
                environment=environment,
                education_level=education_level,
                incident_type=incident_type,
                insurance_reference=insurance_reference,
                settlement_date=settlement_date,
                status=status,
                notes=notes
            )
            
            if success:
                # إرسال إشارة نجاح الحفظ
                self.incident_saved.emit()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء تحديث الحادثة")
        else:
            # إنشاء حادثة جديدة
            incident = self.incident_controller.create_incident(
                student_name=student_name,
                massar_number=massar_number,
                gender=gender,
                institution=institution,
                environment=environment,
                education_level=education_level,
                incident_type=incident_type,
                insurance_reference=insurance_reference,
                settlement_date=settlement_date,
                status=status,
                notes=notes,
                file_number=file_number if file_number else None
            )
            
            if incident:
                # إرسال إشارة نجاح الحفظ
                self.incident_saved.emit()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء إنشاء الحادثة")
    
    def validate_form(self):
        """
        التحقق من صحة النموذج
        
        Returns:
            True إذا كان النموذج صحيحًا، False خلاف ذلك
        """
        # التحقق من اسم التلميذ
        if not self.student_name_input.text().strip():
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال اسم التلميذ")
            self.student_name_input.setFocus()
            return False
        
        # التحقق من المؤسسة
        if not self.institution_input.text().strip():
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال اسم المؤسسة")
            self.institution_input.setFocus()
            return False
        
        return True
    
    def cancel(self):
        """إلغاء العملية"""
        
        # التأكيد قبل الإلغاء
        if self.student_name_input.text().strip() or self.institution_input.text().strip():
            reply = QMessageBox.question(
                self,
                "تأكيد",
                "هل أنت متأكد من رغبتك في إلغاء العملية؟ ستفقد جميع البيانات التي أدخلتها.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.No:
                return
        
        # إرسال إشارة الإلغاء
        self.incident_saved.emit()  # نستخدم نفس الإشارة للعودة إلى الشاشة السابقة
