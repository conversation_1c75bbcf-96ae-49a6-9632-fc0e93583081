"""
نموذج التقارير - يدير عمليات إنشاء وحفظ التقارير
"""
import os
import sys
import datetime
from typing import Optional, List, Dict, Any

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import Report, Incident, ActivityLog, Session
from utils.export import export_to_pdf, export_to_excel

class ReportManager:
    """مدير التقارير"""
    
    @staticmethod
    def create_report(
        title: str,
        report_type: str,
        data: List[Dict[str, Any]],
        headers: List[str],
        file_format: str,
        incident_id: int = None,
        user_id: int = None
    ) -> Optional[Report]:
        """
        إنشاء تقرير جديد
        
        Args:
            title: عنوان التقرير
            report_type: نوع التقرير
            data: بيانات التقرير
            headers: عناوين الأعمدة
            file_format: صيغة الملف (pdf أو excel)
            incident_id: معرف الحادثة (اختياري)
            user_id: معرف المستخدم الذي أنشأ التقرير
            
        Returns:
            كائن التقرير الجديد إذا نجحت العملية، None خلاف ذلك
        """
        session = Session()
        try:
            # تصدير البيانات إلى الملف المطلوب
            filename = title.replace(" ", "_")
            
            if file_format.lower() == 'pdf':
                file_path = export_to_pdf(data, headers, title, filename)
            elif file_format.lower() == 'excel':
                file_path = export_to_excel(data, headers, title, filename)
            else:
                return None
            
            # إنشاء التقرير الجديد
            report = Report(
                title=title,
                report_type=report_type,
                file_path=file_path,
                incident_id=incident_id,
                created_by=user_id
            )
            
            session.add(report)
            session.commit()
            
            # تسجيل نشاط إنشاء التقرير
            if user_id:
                activity = ActivityLog(
                    action="إنشاء تقرير",
                    details=f"تم إنشاء التقرير: {title}",
                    user_id=user_id
                )
                session.add(activity)
                session.commit()
            
            return report
        
        except Exception as e:
            session.rollback()
            print(f"خطأ أثناء إنشاء التقرير: {e}")
            return None
        
        finally:
            session.close()
    
    @staticmethod
    def get_reports(report_type: str = None, start_date: datetime.datetime = None, end_date: datetime.datetime = None) -> List[Dict[str, Any]]:
        """
        الحصول على قائمة التقارير
        
        Args:
            report_type: نوع التقرير (اختياري)
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            
        Returns:
            قائمة بالتقارير المطابقة للمعايير
        """
        session = Session()
        try:
            # بناء الاستعلام
            query = session.query(Report)
            
            # إضافة معايير البحث
            if report_type:
                query = query.filter(Report.report_type == report_type)
            
            if start_date:
                query = query.filter(Report.created_at >= start_date)
            
            if end_date:
                query = query.filter(Report.created_at <= end_date)
            
            # ترتيب النتائج حسب تاريخ الإنشاء (الأحدث أولاً)
            query = query.order_by(Report.created_at.desc())
            
            # تنفيذ الاستعلام
            reports = query.all()
            
            # تحويل النتائج إلى قاموس
            result = []
            for report in reports:
                result.append({
                    'id': report.id,
                    'title': report.title,
                    'report_type': report.report_type,
                    'file_path': report.file_path,
                    'created_at': report.created_at,
                    'incident_id': report.incident_id,
                    'created_by': report.created_by
                })
            
            return result
        
        finally:
            session.close()
    
    @staticmethod
    def get_report_by_id(report_id: int) -> Optional[Dict[str, Any]]:
        """
        الحصول على معلومات التقرير بواسطة المعرف
        
        Args:
            report_id: معرف التقرير
            
        Returns:
            معلومات التقرير إذا وجد، None خلاف ذلك
        """
        session = Session()
        try:
            report = session.query(Report).filter_by(id=report_id).first()
            
            if not report:
                return None
            
            return {
                'id': report.id,
                'title': report.title,
                'report_type': report.report_type,
                'file_path': report.file_path,
                'created_at': report.created_at,
                'incident_id': report.incident_id,
                'created_by': report.created_by
            }
        
        finally:
            session.close()
    
    @staticmethod
    def delete_report(report_id: int, user_id: int = None) -> bool:
        """
        حذف تقرير
        
        Args:
            report_id: معرف التقرير
            user_id: معرف المستخدم الذي قام بالحذف
            
        Returns:
            True إذا نجحت العملية، False خلاف ذلك
        """
        session = Session()
        try:
            # البحث عن التقرير
            report = session.query(Report).filter_by(id=report_id).first()
            if not report:
                return False
            
            # حفظ عنوان التقرير للتسجيل
            title = report.title
            
            # حذف ملف التقرير إذا كان موجودًا
            if report.file_path and os.path.exists(report.file_path):
                try:
                    os.remove(report.file_path)
                except:
                    pass
            
            # حذف التقرير
            session.delete(report)
            session.commit()
            
            # تسجيل نشاط حذف التقرير
            if user_id:
                activity = ActivityLog(
                    action="حذف تقرير",
                    details=f"تم حذف التقرير: {title}",
                    user_id=user_id
                )
                session.add(activity)
                session.commit()
            
            return True
        
        except Exception as e:
            session.rollback()
            print(f"خطأ أثناء حذف التقرير: {e}")
            return False
        
        finally:
            session.close()
    
    @staticmethod
    def generate_daily_report(date: datetime.datetime, user_id: int = None) -> Optional[Report]:
        """
        إنشاء تقرير يومي
        
        Args:
            date: التاريخ
            user_id: معرف المستخدم الذي أنشأ التقرير
            
        Returns:
            كائن التقرير الجديد إذا نجحت العملية، None خلاف ذلك
        """
        session = Session()
        try:
            # تحديد نطاق التاريخ
            start_date = datetime.datetime(date.year, date.month, date.day, 0, 0, 0)
            end_date = datetime.datetime(date.year, date.month, date.day, 23, 59, 59)
            
            # الحصول على الحوادث في هذا اليوم
            incidents = session.query(Incident).filter(
                Incident.created_at >= start_date,
                Incident.created_at <= end_date
            ).all()
            
            # تحويل البيانات إلى التنسيق المطلوب
            data = []
            for incident in incidents:
                data.append({
                    'رقم الملف': incident.file_number,
                    'اسم التلميذ': incident.student_name,
                    'المؤسسة': incident.institution,
                    'نوع الحادثة': incident.incident_type.value,
                    'الحالة': incident.status.value,
                    'تاريخ التسجيل': incident.created_at.strftime('%Y/%m/%d %H:%M')
                })
            
            # عناوين الأعمدة
            headers = ['رقم الملف', 'اسم التلميذ', 'المؤسسة', 'نوع الحادثة', 'الحالة', 'تاريخ التسجيل']
            
            # عنوان التقرير
            title = f"تقرير يومي للحوادث المدرسية - {date.strftime('%Y/%m/%d')}"
            
            # إنشاء التقرير
            return ReportManager.create_report(
                title=title,
                report_type="يومي",
                data=data,
                headers=headers,
                file_format="pdf",
                user_id=user_id
            )
        
        finally:
            session.close()
    
    @staticmethod
    def generate_weekly_report(start_date: datetime.datetime, user_id: int = None) -> Optional[Report]:
        """
        إنشاء تقرير أسبوعي
        
        Args:
            start_date: تاريخ بداية الأسبوع
            user_id: معرف المستخدم الذي أنشأ التقرير
            
        Returns:
            كائن التقرير الجديد إذا نجحت العملية، None خلاف ذلك
        """
        session = Session()
        try:
            # تحديد نطاق التاريخ (أسبوع كامل)
            end_date = start_date + datetime.timedelta(days=6, hours=23, minutes=59, seconds=59)
            
            # الحصول على الحوادث في هذا الأسبوع
            incidents = session.query(Incident).filter(
                Incident.created_at >= start_date,
                Incident.created_at <= end_date
            ).all()
            
            # تحويل البيانات إلى التنسيق المطلوب
            data = []
            for incident in incidents:
                data.append({
                    'رقم الملف': incident.file_number,
                    'اسم التلميذ': incident.student_name,
                    'المؤسسة': incident.institution,
                    'المستوى التعليمي': incident.education_level.value,
                    'نوع الحادثة': incident.incident_type.value,
                    'الحالة': incident.status.value,
                    'تاريخ التسجيل': incident.created_at.strftime('%Y/%m/%d')
                })
            
            # عناوين الأعمدة
            headers = ['رقم الملف', 'اسم التلميذ', 'المؤسسة', 'المستوى التعليمي', 'نوع الحادثة', 'الحالة', 'تاريخ التسجيل']
            
            # عنوان التقرير
            title = f"تقرير أسبوعي للحوادث المدرسية - من {start_date.strftime('%Y/%m/%d')} إلى {end_date.strftime('%Y/%m/%d')}"
            
            # إنشاء التقرير
            return ReportManager.create_report(
                title=title,
                report_type="أسبوعي",
                data=data,
                headers=headers,
                file_format="pdf",
                user_id=user_id
            )
        
        finally:
            session.close()
    
    @staticmethod
    def generate_monthly_report(year: int, month: int, user_id: int = None) -> Optional[Report]:
        """
        إنشاء تقرير شهري
        
        Args:
            year: السنة
            month: الشهر
            user_id: معرف المستخدم الذي أنشأ التقرير
            
        Returns:
            كائن التقرير الجديد إذا نجحت العملية، None خلاف ذلك
        """
        session = Session()
        try:
            # تحديد نطاق التاريخ (شهر كامل)
            start_date = datetime.datetime(year, month, 1, 0, 0, 0)
            if month == 12:
                end_date = datetime.datetime(year + 1, 1, 1, 0, 0, 0) - datetime.timedelta(seconds=1)
            else:
                end_date = datetime.datetime(year, month + 1, 1, 0, 0, 0) - datetime.timedelta(seconds=1)
            
            # الحصول على الحوادث في هذا الشهر
            incidents = session.query(Incident).filter(
                Incident.created_at >= start_date,
                Incident.created_at <= end_date
            ).all()
            
            # تحويل البيانات إلى التنسيق المطلوب
            data = []
            for incident in incidents:
                data.append({
                    'رقم الملف': incident.file_number,
                    'اسم التلميذ': incident.student_name,
                    'المؤسسة': incident.institution,
                    'المستوى التعليمي': incident.education_level.value,
                    'نوع الحادثة': incident.incident_type.value,
                    'الحالة': incident.status.value,
                    'تاريخ التسجيل': incident.created_at.strftime('%Y/%m/%d')
                })
            
            # عناوين الأعمدة
            headers = ['رقم الملف', 'اسم التلميذ', 'المؤسسة', 'المستوى التعليمي', 'نوع الحادثة', 'الحالة', 'تاريخ التسجيل']
            
            # أسماء الأشهر بالعربية
            month_names = {
                1: "يناير", 2: "فبراير", 3: "مارس", 4: "أبريل", 5: "ماي", 6: "يونيو",
                7: "يوليوز", 8: "غشت", 9: "شتنبر", 10: "أكتوبر", 11: "نونبر", 12: "دجنبر"
            }
            
            # عنوان التقرير
            title = f"تقرير شهري للحوادث المدرسية - {month_names[month]} {year}"
            
            # إنشاء التقرير
            return ReportManager.create_report(
                title=title,
                report_type="شهري",
                data=data,
                headers=headers,
                file_format="pdf",
                user_id=user_id
            )
        
        finally:
            session.close()
    
    @staticmethod
    def generate_yearly_report(year: int, user_id: int = None) -> Optional[Report]:
        """
        إنشاء تقرير سنوي
        
        Args:
            year: السنة
            user_id: معرف المستخدم الذي أنشأ التقرير
            
        Returns:
            كائن التقرير الجديد إذا نجحت العملية، None خلاف ذلك
        """
        session = Session()
        try:
            # تحديد نطاق التاريخ (سنة كاملة)
            start_date = datetime.datetime(year, 1, 1, 0, 0, 0)
            end_date = datetime.datetime(year + 1, 1, 1, 0, 0, 0) - datetime.timedelta(seconds=1)
            
            # الحصول على الحوادث في هذه السنة
            incidents = session.query(Incident).filter(
                Incident.created_at >= start_date,
                Incident.created_at <= end_date
            ).all()
            
            # تحويل البيانات إلى التنسيق المطلوب
            data = []
            for incident in incidents:
                data.append({
                    'رقم الملف': incident.file_number,
                    'اسم التلميذ': incident.student_name,
                    'المؤسسة': incident.institution,
                    'المستوى التعليمي': incident.education_level.value,
                    'نوع الحادثة': incident.incident_type.value,
                    'الحالة': incident.status.value,
                    'تاريخ التسجيل': incident.created_at.strftime('%Y/%m/%d')
                })
            
            # عناوين الأعمدة
            headers = ['رقم الملف', 'اسم التلميذ', 'المؤسسة', 'المستوى التعليمي', 'نوع الحادثة', 'الحالة', 'تاريخ التسجيل']
            
            # عنوان التقرير
            title = f"تقرير سنوي للحوادث المدرسية - {year}"
            
            # إنشاء التقرير
            return ReportManager.create_report(
                title=title,
                report_type="سنوي",
                data=data,
                headers=headers,
                file_format="pdf",
                user_id=user_id
            )
        
        finally:
            session.close()
