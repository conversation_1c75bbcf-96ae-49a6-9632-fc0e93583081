"""
نموذج الحادثة - يدير عمليات الحوادث المدرسية
"""
import os
import sys
import datetime
from typing import Optional, List, Dict, Any, Tuple

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import Incident, EducationLevel, Gender, Environment, IncidentType, IncidentStatus, ActivityLog, Session
from utils.helpers import generate_file_number, check_new_year

class IncidentManager:
    """مدير الحوادث"""
    
    @staticmethod
    def create_incident(
        student_name: str,
        massar_number: str,
        gender: Gender,
        institution: str,
        environment: Environment,
        education_level: EducationLevel,
        incident_type: IncidentType,
        insurance_reference: str = None,
        settlement_date: datetime.datetime = None,
        status: IncidentStatus = IncidentStatus.PENDING,
        notes: str = None,
        user_id: int = None,
        file_number: str = None
    ) -> Optional[Incident]:
        """
        إنشاء حادثة جديدة
        
        Args:
            student_name: اسم التلميذ
            massar_number: رقم مسار
            gender: الجنس
            institution: المؤسسة
            environment: الوسط
            education_level: المستوى التعليمي
            incident_type: نوع الحادثة
            insurance_reference: مرجع التأمين (اختياري)
            settlement_date: تاريخ التسوية (اختياري)
            status: حالة الحادثة
            notes: ملاحظات (اختياري)
            user_id: معرف المستخدم الذي أنشأ الحادثة
            file_number: رقم الملف (اختياري، سيتم إنشاؤه تلقائيًا إذا لم يتم توفيره)
            
        Returns:
            كائن الحادثة الجديدة إذا نجحت العملية، None خلاف ذلك
        """
        session = Session()
        try:
            # التحقق من السنة الجديدة وتحديث العدادات إذا لزم الأمر
            check_new_year(session)
            
            # إنشاء رقم الملف إذا لم يتم توفيره
            if not file_number:
                file_number = generate_file_number(session, education_level)
            
            # التحقق من عدم وجود حادثة بنفس رقم الملف
            existing_incident = session.query(Incident).filter_by(file_number=file_number).first()
            if existing_incident:
                return None
            
            # إنشاء الحادثة الجديدة
            incident = Incident(
                file_number=file_number,
                student_name=student_name,
                massar_number=massar_number,
                gender=gender,
                institution=institution,
                environment=environment,
                education_level=education_level,
                incident_type=incident_type,
                insurance_reference=insurance_reference,
                settlement_date=settlement_date,
                status=status,
                notes=notes,
                created_by=user_id
            )
            
            session.add(incident)
            session.commit()
            
            # تسجيل نشاط إنشاء الحادثة
            if user_id:
                activity = ActivityLog(
                    action="إنشاء حادثة",
                    details=f"تم إنشاء الحادثة رقم: {file_number}",
                    user_id=user_id
                )
                session.add(activity)
                session.commit()
            
            return incident
        
        except Exception as e:
            session.rollback()
            print(f"خطأ أثناء إنشاء الحادثة: {e}")
            return None
        
        finally:
            session.close()
    
    @staticmethod
    def update_incident(
        incident_id: int,
        student_name: str = None,
        massar_number: str = None,
        gender: Gender = None,
        institution: str = None,
        environment: Environment = None,
        education_level: EducationLevel = None,
        incident_type: IncidentType = None,
        insurance_reference: str = None,
        settlement_date: datetime.datetime = None,
        status: IncidentStatus = None,
        notes: str = None,
        user_id: int = None
    ) -> bool:
        """
        تحديث بيانات الحادثة
        
        Args:
            incident_id: معرف الحادثة
            student_name: اسم التلميذ (اختياري)
            massar_number: رقم مسار (اختياري)
            gender: الجنس (اختياري)
            institution: المؤسسة (اختياري)
            environment: الوسط (اختياري)
            education_level: المستوى التعليمي (اختياري)
            incident_type: نوع الحادثة (اختياري)
            insurance_reference: مرجع التأمين (اختياري)
            settlement_date: تاريخ التسوية (اختياري)
            status: حالة الحادثة (اختياري)
            notes: ملاحظات (اختياري)
            user_id: معرف المستخدم الذي قام بالتحديث
            
        Returns:
            True إذا نجحت العملية، False خلاف ذلك
        """
        session = Session()
        try:
            # البحث عن الحادثة
            incident = session.query(Incident).filter_by(id=incident_id).first()
            if not incident:
                return False
            
            # تحديث البيانات
            if student_name is not None:
                incident.student_name = student_name
            
            if massar_number is not None:
                incident.massar_number = massar_number
            
            if gender is not None:
                incident.gender = gender
            
            if institution is not None:
                incident.institution = institution
            
            if environment is not None:
                incident.environment = environment
            
            if education_level is not None:
                # إذا تغير المستوى التعليمي، قد نحتاج إلى تغيير رقم الملف
                if incident.education_level != education_level:
                    # هذا يتطلب تنفيذ منطق خاص لتغيير رقم الملف
                    # لكن في هذه النسخة، سنحتفظ برقم الملف الحالي
                    pass
                
                incident.education_level = education_level
            
            if incident_type is not None:
                incident.incident_type = incident_type
            
            if insurance_reference is not None:
                incident.insurance_reference = insurance_reference
            
            if settlement_date is not None:
                incident.settlement_date = settlement_date
            
            if status is not None:
                incident.status = status
            
            if notes is not None:
                incident.notes = notes
            
            # تحديث وقت التعديل
            incident.updated_at = datetime.datetime.now()
            
            session.commit()
            
            # تسجيل نشاط تحديث الحادثة
            if user_id:
                activity = ActivityLog(
                    action="تحديث حادثة",
                    details=f"تم تحديث الحادثة رقم: {incident.file_number}",
                    user_id=user_id
                )
                session.add(activity)
                session.commit()
            
            return True
        
        except Exception as e:
            session.rollback()
            print(f"خطأ أثناء تحديث الحادثة: {e}")
            return False
        
        finally:
            session.close()
    
    @staticmethod
    def delete_incident(incident_id: int, user_id: int = None) -> bool:
        """
        حذف حادثة
        
        Args:
            incident_id: معرف الحادثة
            user_id: معرف المستخدم الذي قام بالحذف
            
        Returns:
            True إذا نجحت العملية، False خلاف ذلك
        """
        session = Session()
        try:
            # البحث عن الحادثة
            incident = session.query(Incident).filter_by(id=incident_id).first()
            if not incident:
                return False
            
            # حفظ رقم الملف للتسجيل
            file_number = incident.file_number
            
            # حذف الحادثة
            session.delete(incident)
            session.commit()
            
            # تسجيل نشاط حذف الحادثة
            if user_id:
                activity = ActivityLog(
                    action="حذف حادثة",
                    details=f"تم حذف الحادثة رقم: {file_number}",
                    user_id=user_id
                )
                session.add(activity)
                session.commit()
            
            return True
        
        except Exception as e:
            session.rollback()
            print(f"خطأ أثناء حذف الحادثة: {e}")
            return False
        
        finally:
            session.close()
    
    @staticmethod
    def get_incident_by_id(incident_id: int) -> Optional[Dict[str, Any]]:
        """
        الحصول على معلومات الحادثة بواسطة المعرف
        
        Args:
            incident_id: معرف الحادثة
            
        Returns:
            معلومات الحادثة إذا وجدت، None خلاف ذلك
        """
        session = Session()
        try:
            incident = session.query(Incident).filter_by(id=incident_id).first()
            
            if not incident:
                return None
            
            return {
                'id': incident.id,
                'file_number': incident.file_number,
                'student_name': incident.student_name,
                'massar_number': incident.massar_number,
                'gender': incident.gender.value,
                'institution': incident.institution,
                'environment': incident.environment.value,
                'education_level': incident.education_level.value,
                'incident_type': incident.incident_type.value,
                'insurance_reference': incident.insurance_reference,
                'settlement_date': incident.settlement_date,
                'status': incident.status.value,
                'created_at': incident.created_at,
                'updated_at': incident.updated_at,
                'notes': incident.notes,
                'created_by': incident.created_by
            }
        
        finally:
            session.close()
    
    @staticmethod
    def get_incident_by_file_number(file_number: str) -> Optional[Dict[str, Any]]:
        """
        الحصول على معلومات الحادثة بواسطة رقم الملف
        
        Args:
            file_number: رقم الملف
            
        Returns:
            معلومات الحادثة إذا وجدت، None خلاف ذلك
        """
        session = Session()
        try:
            incident = session.query(Incident).filter_by(file_number=file_number).first()
            
            if not incident:
                return None
            
            return {
                'id': incident.id,
                'file_number': incident.file_number,
                'student_name': incident.student_name,
                'massar_number': incident.massar_number,
                'gender': incident.gender.value,
                'institution': incident.institution,
                'environment': incident.environment.value,
                'education_level': incident.education_level.value,
                'incident_type': incident.incident_type.value,
                'insurance_reference': incident.insurance_reference,
                'settlement_date': incident.settlement_date,
                'status': incident.status.value,
                'created_at': incident.created_at,
                'updated_at': incident.updated_at,
                'notes': incident.notes,
                'created_by': incident.created_by
            }
        
        finally:
            session.close()
    
    @staticmethod
    def search_incidents(
        student_name: str = None,
        institution: str = None,
        education_level: EducationLevel = None,
        incident_type: IncidentType = None,
        status: IncidentStatus = None,
        gender: Gender = None,
        environment: Environment = None,
        start_date: datetime.datetime = None,
        end_date: datetime.datetime = None,
        page: int = 1,
        per_page: int = 20
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        البحث عن الحوادث حسب معايير مختلفة
        
        Args:
            student_name: اسم التلميذ (اختياري)
            institution: المؤسسة (اختياري)
            education_level: المستوى التعليمي (اختياري)
            incident_type: نوع الحادثة (اختياري)
            status: حالة الحادثة (اختياري)
            gender: الجنس (اختياري)
            environment: الوسط (اختياري)
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            page: رقم الصفحة
            per_page: عدد العناصر في الصفحة
            
        Returns:
            قائمة بالحوادث المطابقة للمعايير وإجمالي عدد النتائج
        """
        session = Session()
        try:
            # بناء الاستعلام
            query = session.query(Incident)
            
            # إضافة معايير البحث
            if student_name:
                query = query.filter(Incident.student_name.like(f"%{student_name}%"))
            
            if institution:
                query = query.filter(Incident.institution.like(f"%{institution}%"))
            
            if education_level:
                query = query.filter(Incident.education_level == education_level)
            
            if incident_type:
                query = query.filter(Incident.incident_type == incident_type)
            
            if status:
                query = query.filter(Incident.status == status)
            
            if gender:
                query = query.filter(Incident.gender == gender)
            
            if environment:
                query = query.filter(Incident.environment == environment)
            
            if start_date:
                query = query.filter(Incident.created_at >= start_date)
            
            if end_date:
                query = query.filter(Incident.created_at <= end_date)
            
            # الحصول على إجمالي عدد النتائج
            total = query.count()
            
            # ترتيب النتائج حسب تاريخ الإنشاء (الأحدث أولاً)
            query = query.order_by(Incident.created_at.desc())
            
            # تقسيم الصفحات
            offset = (page - 1) * per_page
            query = query.offset(offset).limit(per_page)
            
            # تنفيذ الاستعلام
            incidents = query.all()
            
            # تحويل النتائج إلى قاموس
            result = []
            for incident in incidents:
                result.append({
                    'id': incident.id,
                    'file_number': incident.file_number,
                    'student_name': incident.student_name,
                    'massar_number': incident.massar_number,
                    'gender': incident.gender.value,
                    'institution': incident.institution,
                    'environment': incident.environment.value,
                    'education_level': incident.education_level.value,
                    'incident_type': incident.incident_type.value,
                    'insurance_reference': incident.insurance_reference,
                    'settlement_date': incident.settlement_date,
                    'status': incident.status.value,
                    'created_at': incident.created_at,
                    'updated_at': incident.updated_at,
                    'notes': incident.notes,
                    'created_by': incident.created_by
                })
            
            return result, total
        
        finally:
            session.close()
    
    @staticmethod
    def get_statistics() -> Dict[str, Any]:
        """
        الحصول على إحصائيات الحوادث
        
        Returns:
            قاموس يحتوي على إحصائيات مختلفة
        """
        session = Session()
        try:
            # إجمالي عدد الحوادث
            total_incidents = session.query(Incident).count()
            
            # عدد الحوادث حسب المستوى التعليمي
            primary_count = session.query(Incident).filter_by(education_level=EducationLevel.PRIMARY).count()
            middle_count = session.query(Incident).filter_by(education_level=EducationLevel.MIDDLE).count()
            high_count = session.query(Incident).filter_by(education_level=EducationLevel.HIGH).count()
            
            # عدد الحوادث حسب النوع
            school_count = session.query(Incident).filter_by(incident_type=IncidentType.SCHOOL).count()
            transport_count = session.query(Incident).filter_by(incident_type=IncidentType.TRANSPORT).count()
            sport_count = session.query(Incident).filter_by(incident_type=IncidentType.SPORT).count()
            
            # عدد الحوادث حسب الحالة
            pending_count = session.query(Incident).filter_by(status=IncidentStatus.PENDING).count()
            resolved_count = session.query(Incident).filter_by(status=IncidentStatus.RESOLVED).count()
            rejected_count = session.query(Incident).filter_by(status=IncidentStatus.REJECTED).count()
            paid_count = session.query(Incident).filter_by(status=IncidentStatus.PAID).count()
            
            # عدد الحوادث حسب الجنس
            male_count = session.query(Incident).filter_by(gender=Gender.MALE).count()
            female_count = session.query(Incident).filter_by(gender=Gender.FEMALE).count()
            
            # عدد الحوادث حسب الوسط
            urban_count = session.query(Incident).filter_by(environment=Environment.URBAN).count()
            rural_count = session.query(Incident).filter_by(environment=Environment.RURAL).count()
            
            # عدد الحوادث في الشهر الحالي
            now = datetime.datetime.now()
            start_of_month = datetime.datetime(now.year, now.month, 1)
            end_of_month = datetime.datetime(now.year, now.month + 1, 1) if now.month < 12 else datetime.datetime(now.year + 1, 1, 1)
            current_month_count = session.query(Incident).filter(Incident.created_at >= start_of_month, Incident.created_at < end_of_month).count()
            
            # عدد الحوادث في السنة الحالية
            start_of_year = datetime.datetime(now.year, 1, 1)
            end_of_year = datetime.datetime(now.year + 1, 1, 1)
            current_year_count = session.query(Incident).filter(Incident.created_at >= start_of_year, Incident.created_at < end_of_year).count()
            
            # تجميع الإحصائيات
            statistics = {
                'total_incidents': total_incidents,
                'education_level': {
                    'primary': primary_count,
                    'middle': middle_count,
                    'high': high_count
                },
                'incident_type': {
                    'school': school_count,
                    'transport': transport_count,
                    'sport': sport_count
                },
                'status': {
                    'pending': pending_count,
                    'resolved': resolved_count,
                    'rejected': rejected_count,
                    'paid': paid_count
                },
                'gender': {
                    'male': male_count,
                    'female': female_count
                },
                'environment': {
                    'urban': urban_count,
                    'rural': rural_count
                },
                'time_periods': {
                    'current_month': current_month_count,
                    'current_year': current_year_count
                }
            }
            
            return statistics
        
        finally:
            session.close()
