"""
شاشة التقارير
"""
import os
import sys
import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QDateEdit,
    QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
    QAbstractItemView, QMessageBox, QFrame, QGroupBox, QFormLayout,
    QScrollArea, QSpinBox, QTabWidget, QFileDialog
)
from PyQt5.QtCore import Qt, QDate

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from controllers.report_controller import ReportController

class ReportsWidget(QWidget):
    """ويدجت التقارير"""
    
    def __init__(self, report_controller: ReportController):
        """
        تهيئة ويدجت التقارير
        
        Args:
            report_controller: متحكم التقارير
        """
        super().__init__()
        
        self.report_controller = report_controller
        
        # إنشاء التخطيط
        self.setup_ui()
        
        # تحميل التقارير
        self.load_reports()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # علامات التبويب
        self.tabs = QTabWidget()
        
        # تبويب إنشاء التقارير
        self.create_report_tab = QWidget()
        self.setup_create_report_tab()
        self.tabs.addTab(self.create_report_tab, "إنشاء تقرير")
        
        # تبويب قائمة التقارير
        self.reports_list_tab = QWidget()
        self.setup_reports_list_tab()
        self.tabs.addTab(self.reports_list_tab, "قائمة التقارير")
        
        # إضافة علامات التبويب إلى التخطيط الرئيسي
        main_layout.addWidget(self.tabs)
    
    def setup_create_report_tab(self):
        """إعداد تبويب إنشاء التقارير"""
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self.create_report_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # ويدجت المحتوى
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)
        
        # مجموعة نوع التقرير
        report_type_group = QGroupBox("نوع التقرير")
        report_type_layout = QFormLayout()
        
        # نوع التقرير
        self.report_type_input = QComboBox()
        self.report_type_input.addItems(["يومي", "أسبوعي", "شهري", "سنوي"])
        self.report_type_input.currentIndexChanged.connect(self.on_report_type_changed)
        report_type_layout.addRow("نوع التقرير:", self.report_type_input)
        
        report_type_group.setLayout(report_type_layout)
        content_layout.addWidget(report_type_group)
        
        # مجموعة معايير التقرير
        self.report_criteria_group = QGroupBox("معايير التقرير")
        self.report_criteria_layout = QFormLayout()
        
        # التاريخ (للتقرير اليومي)
        self.date_input = QDateEdit()
        self.date_input.setCalendarPopup(True)
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setDisplayFormat("yyyy/MM/dd")
        self.report_criteria_layout.addRow("التاريخ:", self.date_input)
        
        # تاريخ بداية الأسبوع (للتقرير الأسبوعي)
        self.week_start_date_input = QDateEdit()
        self.week_start_date_input.setCalendarPopup(True)
        self.week_start_date_input.setDate(QDate.currentDate().addDays(-QDate.currentDate().dayOfWeek() + 1))
        self.week_start_date_input.setDisplayFormat("yyyy/MM/dd")
        self.report_criteria_layout.addRow("بداية الأسبوع:", self.week_start_date_input)
        
        # الشهر والسنة (للتقرير الشهري)
        self.month_input = QComboBox()
        self.month_input.addItems([
            "يناير", "فبراير", "مارس", "أبريل", "ماي", "يونيو",
            "يوليوز", "غشت", "شتنبر", "أكتوبر", "نونبر", "دجنبر"
        ])
        self.month_input.setCurrentIndex(QDate.currentDate().month() - 1)
        self.report_criteria_layout.addRow("الشهر:", self.month_input)
        
        # السنة (للتقرير الشهري والسنوي)
        self.year_input = QSpinBox()
        self.year_input.setRange(2000, 2100)
        self.year_input.setValue(QDate.currentDate().year())
        self.report_criteria_layout.addRow("السنة:", self.year_input)
        
        # صيغة التقرير
        self.report_format_input = QComboBox()
        self.report_format_input.addItems(["PDF", "Excel"])
        self.report_criteria_layout.addRow("صيغة التقرير:", self.report_format_input)
        
        self.report_criteria_group.setLayout(self.report_criteria_layout)
        content_layout.addWidget(self.report_criteria_group)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        # زر إنشاء التقرير
        self.create_report_button = QPushButton("إنشاء التقرير")
        self.create_report_button.clicked.connect(self.create_report)
        actions_layout.addWidget(self.create_report_button)
        
        content_layout.addLayout(actions_layout)
        
        # إضافة مساحة فارغة
        content_layout.addStretch()
        
        # تعيين ويدجت المحتوى لمنطقة التمرير
        scroll_area.setWidget(content_widget)
        
        # إضافة منطقة التمرير إلى التخطيط الرئيسي
        layout.addWidget(scroll_area)
        
        # تحديث واجهة المستخدم حسب نوع التقرير
        self.on_report_type_changed(0)
    
    def setup_reports_list_tab(self):
        """إعداد تبويب قائمة التقارير"""
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self.reports_list_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)
        
        # مجموعة البحث
        search_group = QGroupBox("بحث")
        search_layout = QHBoxLayout()
        
        # نوع التقرير
        search_layout.addWidget(QLabel("نوع التقرير:"))
        
        self.report_type_search = QComboBox()
        self.report_type_search.addItem("الكل", "")
        self.report_type_search.addItems(["يومي", "أسبوعي", "شهري", "سنوي"])
        search_layout.addWidget(self.report_type_search)
        
        # زر البحث
        self.search_button = QPushButton("بحث")
        self.search_button.clicked.connect(self.load_reports)
        search_layout.addWidget(self.search_button)
        
        search_group.setLayout(search_layout)
        layout.addWidget(search_group)
        
        # جدول التقارير
        self.reports_table = QTableWidget()
        self.reports_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.reports_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.reports_table.setAlternatingRowColors(True)
        self.reports_table.verticalHeader().setVisible(False)
        self.reports_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.reports_table.setColumnCount(5)
        self.reports_table.setHorizontalHeaderLabels([
            "العنوان", "النوع", "تاريخ الإنشاء", "الملف", "إجراءات"
        ])
        # ضبط ارتفاع الصفوف لضمان المحاذاة العمودية للأيقونات
        self.reports_table.verticalHeader().setDefaultSectionSize(45)
        
        layout.addWidget(self.reports_table)
    
    def on_report_type_changed(self, index):
        """
        معالجة تغيير نوع التقرير
        
        Args:
            index: مؤشر العنصر المحدد
        """
        # إخفاء جميع الحقول
        for i in range(self.report_criteria_layout.rowCount()):
            label_item = self.report_criteria_layout.itemAt(i, QFormLayout.LabelRole)
            field_item = self.report_criteria_layout.itemAt(i, QFormLayout.FieldRole)
            
            if label_item and field_item:
                label_widget = label_item.widget()
                field_widget = field_item.widget()
                
                if label_widget and field_widget:
                    label_widget.hide()
                    field_widget.hide()
        
        # إظهار الحقول المناسبة حسب نوع التقرير
        report_type = self.report_type_input.currentText()
        
        if report_type == "يومي":
            # إظهار حقل التاريخ
            self.report_criteria_layout.labelForField(self.date_input).show()
            self.date_input.show()
            
        elif report_type == "أسبوعي":
            # إظهار حقل تاريخ بداية الأسبوع
            self.report_criteria_layout.labelForField(self.week_start_date_input).show()
            self.week_start_date_input.show()
            
        elif report_type == "شهري":
            # إظهار حقول الشهر والسنة
            self.report_criteria_layout.labelForField(self.month_input).show()
            self.month_input.show()
            self.report_criteria_layout.labelForField(self.year_input).show()
            self.year_input.show()
            
        elif report_type == "سنوي":
            # إظهار حقل السنة
            self.report_criteria_layout.labelForField(self.year_input).show()
            self.year_input.show()
        
        # إظهار حقل صيغة التقرير دائمًا
        self.report_criteria_layout.labelForField(self.report_format_input).show()
        self.report_format_input.show()
    
    def set_report_type(self, report_type):
        """
        تعيين نوع التقرير
        
        Args:
            report_type: نوع التقرير
        """
        # تحديد مؤشر نوع التقرير
        index = -1
        
        if report_type == "daily":
            index = 0
        elif report_type == "weekly":
            index = 1
        elif report_type == "monthly":
            index = 2
        elif report_type == "yearly":
            index = 3
        
        if index >= 0:
            # تعيين نوع التقرير
            self.report_type_input.setCurrentIndex(index)
            
            # الانتقال إلى تبويب إنشاء التقارير
            self.tabs.setCurrentIndex(0)
    
    def create_report(self):
        """إنشاء تقرير"""
        
        # الحصول على نوع التقرير
        report_type = self.report_type_input.currentText()
        
        # الحصول على صيغة التقرير
        report_format = self.report_format_input.currentText().lower()
        
        # إنشاء التقرير حسب نوعه
        if report_type == "يومي":
            # الحصول على التاريخ
            date = self.date_input.date().toString("yyyy/MM/dd")
            
            # إنشاء التقرير
            report = self.report_controller.generate_daily_report(date)
            
        elif report_type == "أسبوعي":
            # الحصول على تاريخ بداية الأسبوع
            start_date = self.week_start_date_input.date().toString("yyyy/MM/dd")
            
            # إنشاء التقرير
            report = self.report_controller.generate_weekly_report(start_date)
            
        elif report_type == "شهري":
            # الحصول على الشهر والسنة
            month = self.month_input.currentIndex() + 1
            year = self.year_input.value()
            
            # إنشاء التقرير
            report = self.report_controller.generate_monthly_report(year, month)
            
        elif report_type == "سنوي":
            # الحصول على السنة
            year = self.year_input.value()
            
            # إنشاء التقرير
            report = self.report_controller.generate_yearly_report(year)
        
        # التحقق من نجاح إنشاء التقرير
        if report:
            QMessageBox.information(self, "معلومات", "تم إنشاء التقرير بنجاح")
            
            # تحديث قائمة التقارير
            self.load_reports()
            
            # الانتقال إلى تبويب قائمة التقارير
            self.tabs.setCurrentIndex(1)
            
            # فتح التقرير
            self.open_report(report.get('file_path'))
        else:
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء إنشاء التقرير")
    
    def load_reports(self):
        """تحميل قائمة التقارير"""
        
        # الحصول على نوع التقرير للبحث
        report_type = self.report_type_search.currentText()
        if report_type == "الكل":
            report_type = None
        
        # الحصول على قائمة التقارير
        reports = self.report_controller.get_reports(report_type)
        
        # مسح الجدول
        self.reports_table.setRowCount(0)
        
        # إضافة التقارير إلى الجدول
        for row, report in enumerate(reports):
            self.reports_table.insertRow(row)
            
            # العنوان
            self.reports_table.setItem(row, 0, QTableWidgetItem(report.get('title', '')))
            
            # النوع
            self.reports_table.setItem(row, 1, QTableWidgetItem(report.get('report_type', '')))
            
            # تاريخ الإنشاء
            created_at = report.get('created_at')
            if created_at:
                if isinstance(created_at, str):
                    created_at_text = created_at
                elif isinstance(created_at, datetime.datetime):
                    created_at_text = created_at.strftime('%Y/%m/%d %H:%M')
                else:
                    created_at_text = ''
            else:
                created_at_text = ''
            
            self.reports_table.setItem(row, 2, QTableWidgetItem(created_at_text))
            
            # الملف
            file_path = report.get('file_path', '')
            file_name = os.path.basename(file_path) if file_path else ''
            self.reports_table.setItem(row, 3, QTableWidgetItem(file_name))
            
            # إجراءات
            actions_layout = QHBoxLayout()
            actions_layout.setContentsMargins(0, 0, 0, 0)
            actions_layout.setSpacing(5)
            # محاذاة الأيقونات في الوسط أفقياً وعمودياً
            actions_layout.setAlignment(Qt.AlignCenter)

            # زر فتح التقرير
            open_button = QPushButton("📂 فتح")
            open_button.setProperty('file_path', file_path)
            open_button.clicked.connect(self.on_open_button_clicked)
            open_button.setToolTip("فتح التقرير")
            open_button.setCursor(Qt.PointingHandCursor)
            actions_layout.addWidget(open_button, 0, Qt.AlignCenter)

            # زر حذف التقرير
            delete_button = QPushButton("🗑️ حذف")
            delete_button.setProperty('report_id', report.get('id'))
            delete_button.clicked.connect(self.on_delete_button_clicked)
            delete_button.setToolTip("حذف التقرير نهائياً")
            delete_button.setCursor(Qt.PointingHandCursor)
            actions_layout.addWidget(delete_button, 0, Qt.AlignCenter)

            # إنشاء ويدجت للإجراءات
            actions_widget = QWidget()
            actions_widget.setObjectName("actions_widget")
            actions_widget.setLayout(actions_layout)
            # ضبط خصائص الويدجت للمحاذاة العمودية المثالية
            actions_widget.setFixedHeight(45)  # نفس ارتفاع الصف
            actions_widget.setStyleSheet("""
                QWidget#actions_widget {
                    border: none;
                    background: transparent;
                    border-radius: 4px;
                }
                QWidget#actions_widget:hover {
                    background: rgba(52, 152, 219, 0.05);
                }
            """)

            # إضافة ويدجت الإجراءات إلى الجدول
            self.reports_table.setCellWidget(row, 4, actions_widget)
    
    def on_open_button_clicked(self):
        """معالجة النقر على زر فتح التقرير"""
        
        # الحصول على مسار الملف
        button = self.sender()
        file_path = button.property('file_path')
        
        # فتح التقرير
        self.open_report(file_path)
    
    def on_delete_button_clicked(self):
        """معالجة النقر على زر حذف التقرير"""
        
        # الحصول على معرف التقرير
        button = self.sender()
        report_id = button.property('report_id')
        
        # التأكيد قبل الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد",
            "هل أنت متأكد من رغبتك في حذف هذا التقرير؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # حذف التقرير
            success = self.report_controller.delete_report(report_id)
            
            if success:
                QMessageBox.information(self, "معلومات", "تم حذف التقرير بنجاح")
                self.load_reports()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حذف التقرير")
    
    def open_report(self, file_path):
        """
        فتح التقرير
        
        Args:
            file_path: مسار ملف التقرير
        """
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "تنبيه", "ملف التقرير غير موجود")
            return
        
        # فتح الملف باستخدام التطبيق الافتراضي
        import subprocess
        if sys.platform == "win32":
            os.startfile(file_path)
        elif sys.platform == "darwin":
            subprocess.call(["open", file_path])
        else:
            subprocess.call(["xdg-open", file_path])
