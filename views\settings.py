"""
شاشة الإعدادات
"""
import os
import sys
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton,
    QTabWidget, QFormLayout, QGroupBox, QMessageBox, QFileDialog,
    QListWidget, QListWidgetItem, QAbstractItemView, QDialog, QComboBox,
    QCheckBox, QScrollArea, QFrame, QSpinBox
)
from PyQt5.QtCore import Qt

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from controllers.auth_controller import AuthController
from controllers.backup_controller import BackupController

class PasswordChangeDialog(QDialog):
    """مربع حوار تغيير كلمة المرور"""
    
    def __init__(self, auth_controller: AuthController, parent=None):
        """
        تهيئة مربع حوار تغيير كلمة المرور
        
        Args:
            auth_controller: متحكم المصادقة
            parent: الويدجت الأب
        """
        super().__init__(parent)
        
        self.auth_controller = auth_controller
        
        # إعداد مربع الحوار
        self.setWindowTitle("تغيير كلمة المرور")
        self.setMinimumWidth(300)
        
        # إنشاء التخطيط
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # نموذج تغيير كلمة المرور
        form_layout = QFormLayout()
        
        # كلمة المرور الحالية
        self.current_password_input = QLineEdit()
        self.current_password_input.setEchoMode(QLineEdit.Password)
        form_layout.addRow("كلمة المرور الحالية:", self.current_password_input)
        
        # كلمة المرور الجديدة
        self.new_password_input = QLineEdit()
        self.new_password_input.setEchoMode(QLineEdit.Password)
        form_layout.addRow("كلمة المرور الجديدة:", self.new_password_input)
        
        # تأكيد كلمة المرور الجديدة
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setEchoMode(QLineEdit.Password)
        form_layout.addRow("تأكيد كلمة المرور الجديدة:", self.confirm_password_input)
        
        layout.addLayout(form_layout)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        # زر الحفظ
        self.save_button = QPushButton("حفظ")
        self.save_button.clicked.connect(self.change_password)
        buttons_layout.addWidget(self.save_button)
        
        # زر الإلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)
        
        layout.addLayout(buttons_layout)
    
    def change_password(self):
        """تغيير كلمة المرور"""
        
        # الحصول على كلمات المرور
        current_password = self.current_password_input.text()
        new_password = self.new_password_input.text()
        confirm_password = self.confirm_password_input.text()
        
        # التحقق من إدخال كلمة المرور الحالية
        if not current_password:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال كلمة المرور الحالية")
            self.current_password_input.setFocus()
            return
        
        # التحقق من إدخال كلمة المرور الجديدة
        if not new_password:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال كلمة المرور الجديدة")
            self.new_password_input.setFocus()
            return
        
        # التحقق من تأكيد كلمة المرور الجديدة
        if new_password != confirm_password:
            QMessageBox.warning(self, "تنبيه", "كلمة المرور الجديدة وتأكيدها غير متطابقين")
            self.confirm_password_input.setFocus()
            return
        
        # التحقق من طول كلمة المرور الجديدة
        if len(new_password) < 6:
            QMessageBox.warning(self, "تنبيه", "يجب أن تكون كلمة المرور الجديدة 6 أحرف على الأقل")
            self.new_password_input.setFocus()
            return
        
        # تغيير كلمة المرور
        success = self.auth_controller.change_password(current_password, new_password)
        
        if success:
            QMessageBox.information(self, "معلومات", "تم تغيير كلمة المرور بنجاح")
            self.accept()
        else:
            QMessageBox.critical(self, "خطأ", "كلمة المرور الحالية غير صحيحة")
            self.current_password_input.setFocus()

class SettingsWidget(QWidget):
    """ويدجت الإعدادات"""
    
    def __init__(self, auth_controller: AuthController, backup_controller: BackupController):
        """
        تهيئة ويدجت الإعدادات
        
        Args:
            auth_controller: متحكم المصادقة
            backup_controller: متحكم النسخ الاحتياطي
        """
        super().__init__()
        
        self.auth_controller = auth_controller
        self.backup_controller = backup_controller
        
        # إنشاء التخطيط
        self.setup_ui()
        
        # تحميل النسخ الاحتياطية
        self.load_backups()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # علامات التبويب
        self.tabs = QTabWidget()
        
        # تبويب الحساب
        self.account_tab = QWidget()
        self.setup_account_tab()
        self.tabs.addTab(self.account_tab, "الحساب")
        
        # تبويب النسخ الاحتياطي
        self.backup_tab = QWidget()
        self.setup_backup_tab()
        self.tabs.addTab(self.backup_tab, "النسخ الاحتياطي")
        
        # تبويب الإعدادات العامة
        self.general_tab = QWidget()
        self.setup_general_tab()
        self.tabs.addTab(self.general_tab, "إعدادات عامة")
        
        # إضافة علامات التبويب إلى التخطيط الرئيسي
        main_layout.addWidget(self.tabs)
    
    def setup_account_tab(self):
        """إعداد تبويب الحساب"""
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self.account_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # ويدجت المحتوى
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)
        
        # مجموعة معلومات المستخدم
        user_info_group = QGroupBox("معلومات المستخدم")
        user_info_layout = QFormLayout()
        
        # الحصول على معلومات المستخدم الحالي
        user_info = self.auth_controller.get_current_user()
        
        # اسم المستخدم
        username_label = QLabel(user_info.get('username', ''))
        user_info_layout.addRow("اسم المستخدم:", username_label)
        
        # الاسم الكامل
        full_name_label = QLabel(user_info.get('full_name', ''))
        user_info_layout.addRow("الاسم الكامل:", full_name_label)
        
        # البريد الإلكتروني
        email_label = QLabel(user_info.get('email', ''))
        user_info_layout.addRow("البريد الإلكتروني:", email_label)
        
        # الدور
        role_label = QLabel(user_info.get('role', ''))
        user_info_layout.addRow("الدور:", role_label)
        
        # آخر تسجيل دخول
        last_login = user_info.get('last_login')
        if last_login:
            if isinstance(last_login, str):
                last_login_text = last_login
            else:
                last_login_text = last_login.strftime('%Y/%m/%d %H:%M')
        else:
            last_login_text = ''
        
        last_login_label = QLabel(last_login_text)
        user_info_layout.addRow("آخر تسجيل دخول:", last_login_label)
        
        user_info_group.setLayout(user_info_layout)
        content_layout.addWidget(user_info_group)
        
        # مجموعة تغيير كلمة المرور
        password_group = QGroupBox("تغيير كلمة المرور")
        password_layout = QVBoxLayout()
        
        # زر تغيير كلمة المرور
        change_password_button = QPushButton("تغيير كلمة المرور")
        change_password_button.clicked.connect(self.show_change_password_dialog)
        password_layout.addWidget(change_password_button)
        
        password_group.setLayout(password_layout)
        content_layout.addWidget(password_group)
        
        # إضافة مساحة فارغة
        content_layout.addStretch()
        
        # تعيين ويدجت المحتوى لمنطقة التمرير
        scroll_area.setWidget(content_widget)
        
        # إضافة منطقة التمرير إلى التخطيط الرئيسي
        layout.addWidget(scroll_area)
    
    def setup_backup_tab(self):
        """إعداد تبويب النسخ الاحتياطي"""
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self.backup_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # ويدجت المحتوى
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)
        
        # مجموعة إنشاء نسخة احتياطية
        create_backup_group = QGroupBox("إنشاء نسخة احتياطية")
        create_backup_layout = QVBoxLayout()
        
        # نموذج إنشاء نسخة احتياطية
        form_layout = QFormLayout()
        
        # وصف النسخة الاحتياطية
        self.backup_description_input = QLineEdit()
        form_layout.addRow("وصف النسخة الاحتياطية:", self.backup_description_input)
        
        create_backup_layout.addLayout(form_layout)
        
        # زر إنشاء نسخة احتياطية
        create_backup_button = QPushButton("إنشاء نسخة احتياطية")
        create_backup_button.clicked.connect(self.create_backup)
        create_backup_layout.addWidget(create_backup_button)
        
        create_backup_group.setLayout(create_backup_layout)
        content_layout.addWidget(create_backup_group)
        
        # مجموعة النسخ الاحتياطية المتوفرة
        backups_group = QGroupBox("النسخ الاحتياطية المتوفرة")
        backups_layout = QVBoxLayout()
        
        # قائمة النسخ الاحتياطية
        self.backups_list = QListWidget()
        self.backups_list.setSelectionMode(QAbstractItemView.SingleSelection)
        backups_layout.addWidget(self.backups_list)
        
        # أزرار النسخ الاحتياطية
        buttons_layout = QHBoxLayout()
        
        # زر استعادة النسخة الاحتياطية
        self.restore_backup_button = QPushButton("استعادة")
        self.restore_backup_button.clicked.connect(self.restore_backup)
        buttons_layout.addWidget(self.restore_backup_button)
        
        # زر حذف النسخة الاحتياطية
        self.delete_backup_button = QPushButton("حذف")
        self.delete_backup_button.clicked.connect(self.delete_backup)
        buttons_layout.addWidget(self.delete_backup_button)
        
        backups_layout.addLayout(buttons_layout)
        
        backups_group.setLayout(backups_layout)
        content_layout.addWidget(backups_group)
        
        # تعيين ويدجت المحتوى لمنطقة التمرير
        scroll_area.setWidget(content_widget)
        
        # إضافة منطقة التمرير إلى التخطيط الرئيسي
        layout.addWidget(scroll_area)
    
    def setup_general_tab(self):
        """إعداد تبويب الإعدادات العامة"""
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(self.general_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # ويدجت المحتوى
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)
        
        # مجموعة إعدادات واجهة المستخدم
        ui_group = QGroupBox("واجهة المستخدم")
        ui_layout = QFormLayout()
        
        # السمة
        self.theme_input = QComboBox()
        self.theme_input.addItems(["فاتح", "داكن"])
        ui_layout.addRow("السمة:", self.theme_input)
        
        # حجم الخط
        self.font_size_input = QSpinBox()
        self.font_size_input.setRange(8, 16)
        self.font_size_input.setValue(10)
        ui_layout.addRow("حجم الخط:", self.font_size_input)
        
        ui_group.setLayout(ui_layout)
        content_layout.addWidget(ui_group)
        
        # مجموعة إعدادات التقارير
        reports_group = QGroupBox("التقارير")
        reports_layout = QFormLayout()
        
        # الصيغة الافتراضية للتقارير
        self.default_report_format_input = QComboBox()
        self.default_report_format_input.addItems(["PDF", "Excel"])
        reports_layout.addRow("الصيغة الافتراضية للتقارير:", self.default_report_format_input)
        
        reports_group.setLayout(reports_layout)
        content_layout.addWidget(reports_group)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        # زر حفظ الإعدادات
        save_settings_button = QPushButton("حفظ الإعدادات")
        save_settings_button.clicked.connect(self.save_settings)
        actions_layout.addWidget(save_settings_button)
        
        # زر استعادة الإعدادات الافتراضية
        reset_settings_button = QPushButton("استعادة الإعدادات الافتراضية")
        reset_settings_button.clicked.connect(self.reset_settings)
        actions_layout.addWidget(reset_settings_button)
        
        content_layout.addLayout(actions_layout)
        
        # إضافة مساحة فارغة
        content_layout.addStretch()
        
        # تعيين ويدجت المحتوى لمنطقة التمرير
        scroll_area.setWidget(content_widget)
        
        # إضافة منطقة التمرير إلى التخطيط الرئيسي
        layout.addWidget(scroll_area)
    
    def show_change_password_dialog(self):
        """عرض مربع حوار تغيير كلمة المرور"""
        
        dialog = PasswordChangeDialog(self.auth_controller, self)
        dialog.exec_()
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        
        # التحقق من صلاحيات المدير
        if not self.auth_controller.is_admin():
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية إنشاء نسخة احتياطية")
            return
        
        # الحصول على وصف النسخة الاحتياطية
        description = self.backup_description_input.text().strip()
        
        # إنشاء النسخة الاحتياطية
        backup_path = self.backup_controller.create_backup(description)
        
        if backup_path:
            QMessageBox.information(self, "معلومات", "تم إنشاء النسخة الاحتياطية بنجاح")
            
            # مسح حقل الوصف
            self.backup_description_input.clear()
            
            # تحديث قائمة النسخ الاحتياطية
            self.load_backups()
        else:
            QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء إنشاء النسخة الاحتياطية")
    
    def load_backups(self):
        """تحميل قائمة النسخ الاحتياطية"""
        
        # الحصول على قائمة النسخ الاحتياطية
        backups = self.backup_controller.get_backups()
        
        # مسح القائمة
        self.backups_list.clear()
        
        # إضافة النسخ الاحتياطية إلى القائمة
        for backup in backups:
            # إنشاء عنصر القائمة
            item = QListWidgetItem()
            
            # تعيين نص العنصر
            item_text = f"{backup.get('date')} - {backup.get('description') or 'بدون وصف'} ({backup.get('size')} KB)"
            item.setText(item_text)
            
            # تخزين مسار النسخة الاحتياطية في العنصر
            item.setData(Qt.UserRole, backup.get('path'))
            
            # إضافة العنصر إلى القائمة
            self.backups_list.addItem(item)
    
    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        
        # التحقق من صلاحيات المدير
        if not self.auth_controller.is_admin():
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية استعادة نسخة احتياطية")
            return
        
        # الحصول على العنصر المحدد
        selected_items = self.backups_list.selectedItems()
        
        if not selected_items:
            QMessageBox.warning(self, "تنبيه", "يرجى تحديد نسخة احتياطية")
            return
        
        # الحصول على مسار النسخة الاحتياطية
        backup_path = selected_items[0].data(Qt.UserRole)
        
        # التأكيد قبل الاستعادة
        reply = QMessageBox.question(
            self,
            "تأكيد",
            "هل أنت متأكد من رغبتك في استعادة هذه النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # استعادة النسخة الاحتياطية
            success = self.backup_controller.restore_backup(backup_path)
            
            if success:
                QMessageBox.information(self, "معلومات", "تم استعادة النسخة الاحتياطية بنجاح. يرجى إعادة تشغيل التطبيق.")
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء استعادة النسخة الاحتياطية")
    
    def delete_backup(self):
        """حذف نسخة احتياطية"""
        
        # التحقق من صلاحيات المدير
        if not self.auth_controller.is_admin():
            QMessageBox.warning(self, "تنبيه", "ليس لديك صلاحية حذف نسخة احتياطية")
            return
        
        # الحصول على العنصر المحدد
        selected_items = self.backups_list.selectedItems()
        
        if not selected_items:
            QMessageBox.warning(self, "تنبيه", "يرجى تحديد نسخة احتياطية")
            return
        
        # الحصول على مسار النسخة الاحتياطية
        backup_path = selected_items[0].data(Qt.UserRole)
        
        # التأكيد قبل الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد",
            "هل أنت متأكد من رغبتك في حذف هذه النسخة الاحتياطية؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # حذف النسخة الاحتياطية
            success = self.backup_controller.delete_backup(backup_path)
            
            if success:
                QMessageBox.information(self, "معلومات", "تم حذف النسخة الاحتياطية بنجاح")
                
                # تحديث قائمة النسخ الاحتياطية
                self.load_backups()
            else:
                QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء حذف النسخة الاحتياطية")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        
        # الحصول على الإعدادات
        theme = self.theme_input.currentText()
        font_size = self.font_size_input.value()
        default_report_format = self.default_report_format_input.currentText()
        
        # حفظ الإعدادات (هذه الوظيفة غير مكتملة حاليًا)
        QMessageBox.information(self, "معلومات", "تم حفظ الإعدادات بنجاح")
    
    def reset_settings(self):
        """استعادة الإعدادات الافتراضية"""
        
        # التأكيد قبل الاستعادة
        reply = QMessageBox.question(
            self,
            "تأكيد",
            "هل أنت متأكد من رغبتك في استعادة الإعدادات الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # استعادة الإعدادات الافتراضية
            self.theme_input.setCurrentIndex(0)  # فاتح
            self.font_size_input.setValue(10)
            self.default_report_format_input.setCurrentIndex(0)  # PDF
            
            QMessageBox.information(self, "معلومات", "تم استعادة الإعدادات الافتراضية بنجاح")
