"""
نموذج النسخ الاحتياطي - يدير عمليات النسخ الاحتياطي واستعادة البيانات
"""
import os
import sys
import shutil
import sqlite3
import datetime
import zipfile
from pathlib import Path

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import DB_PATH, BACKUP_DIR

class BackupManager:
    """مدير النسخ الاحتياطي"""
    
    @staticmethod
    def create_backup(description: str = "") -> str:
        """
        إنشاء نسخة احتياطية من قاعدة البيانات
        
        Args:
            description: وصف النسخة الاحتياطية
            
        Returns:
            مسار ملف النسخة الاحتياطية
        """
        # التأكد من وجود مجلد النسخ الاحتياطية
        os.makedirs(BACKUP_DIR, exist_ok=True)
        
        # إنشاء اسم الملف
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"backup_{timestamp}.zip"
        backup_path = os.path.join(BACKUP_DIR, backup_filename)
        
        # إنشاء ملف الوصف
        description_file = os.path.join(BACKUP_DIR, "temp_description.txt")
        with open(description_file, 'w', encoding='utf-8') as f:
            f.write(f"تاريخ النسخ الاحتياطي: {datetime.datetime.now().strftime('%Y/%m/%d %H:%M:%S')}\n")
            f.write(f"الوصف: {description}\n")
        
        # إنشاء ملف الضغط
        with zipfile.ZipFile(backup_path, 'w') as zipf:
            # إضافة قاعدة البيانات
            if os.path.exists(DB_PATH):
                # إنشاء نسخة مؤقتة من قاعدة البيانات
                temp_db = os.path.join(BACKUP_DIR, "temp_db.sqlite")
                
                # نسخ قاعدة البيانات إلى الملف المؤقت
                conn = sqlite3.connect(DB_PATH)
                backup_conn = sqlite3.connect(temp_db)
                conn.backup(backup_conn)
                conn.close()
                backup_conn.close()
                
                # إضافة الملف المؤقت إلى ملف الضغط
                zipf.write(temp_db, os.path.basename(DB_PATH))
                
                # حذف الملف المؤقت
                os.remove(temp_db)
            
            # إضافة ملف الوصف
            zipf.write(description_file, "description.txt")
        
        # حذف ملف الوصف المؤقت
        os.remove(description_file)
        
        return backup_path
    
    @staticmethod
    def restore_backup(backup_path: str) -> bool:
        """
        استعادة قاعدة البيانات من نسخة احتياطية
        
        Args:
            backup_path: مسار ملف النسخة الاحتياطية
            
        Returns:
            True إذا تمت الاستعادة بنجاح، False خلاف ذلك
        """
        try:
            # التحقق من وجود ملف النسخة الاحتياطية
            if not os.path.exists(backup_path):
                return False
            
            # إنشاء مجلد مؤقت للاستخراج
            temp_dir = os.path.join(BACKUP_DIR, "temp_restore")
            os.makedirs(temp_dir, exist_ok=True)
            
            # استخراج ملفات النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                zipf.extractall(temp_dir)
            
            # مسار قاعدة البيانات المستخرجة
            extracted_db = os.path.join(temp_dir, os.path.basename(DB_PATH))
            
            # التحقق من وجود قاعدة البيانات المستخرجة
            if not os.path.exists(extracted_db):
                shutil.rmtree(temp_dir)
                return False
            
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستبدال
            if os.path.exists(DB_PATH):
                backup_before_restore = BackupManager.create_backup("نسخة احتياطية قبل الاستعادة")
            
            # إغلاق أي اتصالات مفتوحة بقاعدة البيانات
            # (هذا قد يتطلب إعادة تشغيل التطبيق)
            
            # نسخ قاعدة البيانات المستخرجة إلى المسار الأصلي
            os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
            shutil.copy2(extracted_db, DB_PATH)
            
            # تنظيف المجلد المؤقت
            shutil.rmtree(temp_dir)
            
            return True
        
        except Exception as e:
            print(f"خطأ أثناء استعادة النسخة الاحتياطية: {e}")
            return False
    
    @staticmethod
    def get_backups() -> list:
        """
        الحصول على قائمة النسخ الاحتياطية المتوفرة
        
        Returns:
            قائمة بمعلومات النسخ الاحتياطية
        """
        backups = []
        
        # التأكد من وجود مجلد النسخ الاحتياطية
        if not os.path.exists(BACKUP_DIR):
            return backups
        
        # البحث عن ملفات النسخ الاحتياطية
        for filename in os.listdir(BACKUP_DIR):
            if filename.startswith("backup_") and filename.endswith(".zip"):
                file_path = os.path.join(BACKUP_DIR, filename)
                
                # استخراج التاريخ من اسم الملف
                try:
                    date_str = filename.replace("backup_", "").replace(".zip", "")
                    date = datetime.datetime.strptime(date_str, "%Y%m%d_%H%M%S")
                    formatted_date = date.strftime("%Y/%m/%d %H:%M:%S")
                except:
                    formatted_date = "غير معروف"
                
                # استخراج الوصف من ملف النسخة الاحتياطية
                description = ""
                try:
                    with zipfile.ZipFile(file_path, 'r') as zipf:
                        if "description.txt" in zipf.namelist():
                            with zipf.open("description.txt") as desc_file:
                                lines = desc_file.read().decode('utf-8').splitlines()
                                if len(lines) > 1:
                                    description = lines[1].replace("الوصف: ", "")
                except:
                    pass
                
                # إضافة معلومات النسخة الاحتياطية إلى القائمة
                backups.append({
                    'filename': filename,
                    'path': file_path,
                    'date': formatted_date,
                    'description': description,
                    'size': os.path.getsize(file_path) // 1024  # الحجم بالكيلوبايت
                })
        
        # ترتيب النسخ الاحتياطية حسب التاريخ (الأحدث أولاً)
        backups.sort(key=lambda x: x['filename'], reverse=True)
        
        return backups
    
    @staticmethod
    def delete_backup(backup_path: str) -> bool:
        """
        حذف نسخة احتياطية
        
        Args:
            backup_path: مسار ملف النسخة الاحتياطية
            
        Returns:
            True إذا تم الحذف بنجاح، False خلاف ذلك
        """
        try:
            if os.path.exists(backup_path):
                os.remove(backup_path)
                return True
            return False
        except:
            return False
