"""
نموذج المستخدم - يدير عمليات المستخدمين والمصادقة
"""
import os
import sys
import datetime
from typing import Optional, List, Dict, Any

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import User, UserRole, ActivityLog, Session
from utils.helpers import hash_password, verify_password

class UserManager:
    """مدير المستخدمين"""
    
    @staticmethod
    def authenticate(username: str, password: str) -> Optional[Dict[str, Any]]:
        """
        مصادقة المستخدم

        Args:
            username: اسم المستخدم
            password: كلمة المرور

        Returns:
            معلومات المستخدم إذا نجحت المصادقة، None خلاف ذلك
        """
        session = Session()
        try:
            # البحث عن المستخدم
            user = session.query(User).filter_by(username=username).first()

            # التحقق من وجود المستخدم وصحة كلمة المرور
            if user and verify_password(user.password_hash, password):
                # تحديث وقت آخر تسجيل دخول
                user.last_login = datetime.datetime.now()
                session.commit()

                # تسجيل نشاط تسجيل الدخول
                activity = ActivityLog(
                    action="تسجيل الدخول",
                    user_id=user.id
                )
                session.add(activity)
                session.commit()

                # إرجاع معلومات المستخدم كـ dictionary
                return {
                    'id': user.id,
                    'username': user.username,
                    'full_name': user.full_name,
                    'email': user.email,
                    'role': user.role,
                    'is_active': user.is_active,
                    'last_login': user.last_login
                }

            return None

        finally:
            session.close()
    
    @staticmethod
    def create_user(username: str, password: str, full_name: str, email: str = None, role: UserRole = UserRole.USER) -> Optional[User]:
        """
        إنشاء مستخدم جديد
        
        Args:
            username: اسم المستخدم
            password: كلمة المرور
            full_name: الاسم الكامل
            email: البريد الإلكتروني (اختياري)
            role: دور المستخدم
            
        Returns:
            كائن المستخدم الجديد إذا نجحت العملية، None خلاف ذلك
        """
        session = Session()
        try:
            # التحقق من عدم وجود مستخدم بنفس اسم المستخدم
            existing_user = session.query(User).filter_by(username=username).first()
            if existing_user:
                return None
            
            # إنشاء المستخدم الجديد
            user = User(
                username=username,
                password_hash=hash_password(password),
                full_name=full_name,
                email=email,
                role=role
            )
            
            session.add(user)
            session.commit()
            
            # تسجيل نشاط إنشاء المستخدم
            activity = ActivityLog(
                action="إنشاء مستخدم",
                details=f"تم إنشاء المستخدم: {username}",
                user_id=user.id
            )
            session.add(activity)
            session.commit()
            
            return user
        
        except Exception as e:
            session.rollback()
            print(f"خطأ أثناء إنشاء المستخدم: {e}")
            return None
        
        finally:
            session.close()
    
    @staticmethod
    def update_user(user_id: int, full_name: str = None, email: str = None, is_active: bool = None, role: UserRole = None) -> bool:
        """
        تحديث بيانات المستخدم
        
        Args:
            user_id: معرف المستخدم
            full_name: الاسم الكامل (اختياري)
            email: البريد الإلكتروني (اختياري)
            is_active: حالة المستخدم (اختياري)
            role: دور المستخدم (اختياري)
            
        Returns:
            True إذا نجحت العملية، False خلاف ذلك
        """
        session = Session()
        try:
            # البحث عن المستخدم
            user = session.query(User).filter_by(id=user_id).first()
            if not user:
                return False
            
            # تحديث البيانات
            if full_name is not None:
                user.full_name = full_name
            
            if email is not None:
                user.email = email
            
            if is_active is not None:
                user.is_active = is_active
            
            if role is not None:
                user.role = role
            
            session.commit()
            
            # تسجيل نشاط تحديث المستخدم
            activity = ActivityLog(
                action="تحديث بيانات المستخدم",
                details=f"تم تحديث بيانات المستخدم: {user.username}",
                user_id=user_id
            )
            session.add(activity)
            session.commit()
            
            return True
        
        except Exception as e:
            session.rollback()
            print(f"خطأ أثناء تحديث المستخدم: {e}")
            return False
        
        finally:
            session.close()
    
    @staticmethod
    def change_password(user_id: int, current_password: str, new_password: str) -> bool:
        """
        تغيير كلمة مرور المستخدم
        
        Args:
            user_id: معرف المستخدم
            current_password: كلمة المرور الحالية
            new_password: كلمة المرور الجديدة
            
        Returns:
            True إذا نجحت العملية، False خلاف ذلك
        """
        session = Session()
        try:
            # البحث عن المستخدم
            user = session.query(User).filter_by(id=user_id).first()
            if not user:
                return False
            
            # التحقق من صحة كلمة المرور الحالية
            if not verify_password(user.password_hash, current_password):
                return False
            
            # تحديث كلمة المرور
            user.password_hash = hash_password(new_password)
            session.commit()
            
            # تسجيل نشاط تغيير كلمة المرور
            activity = ActivityLog(
                action="تغيير كلمة المرور",
                user_id=user_id
            )
            session.add(activity)
            session.commit()
            
            return True
        
        except Exception as e:
            session.rollback()
            print(f"خطأ أثناء تغيير كلمة المرور: {e}")
            return False
        
        finally:
            session.close()
    
    @staticmethod
    def get_users() -> List[Dict[str, Any]]:
        """
        الحصول على قائمة المستخدمين
        
        Returns:
            قائمة بمعلومات المستخدمين
        """
        session = Session()
        try:
            users = session.query(User).all()
            
            result = []
            for user in users:
                result.append({
                    'id': user.id,
                    'username': user.username,
                    'full_name': user.full_name,
                    'email': user.email,
                    'role': user.role.value,
                    'is_active': user.is_active,
                    'created_at': user.created_at,
                    'last_login': user.last_login
                })
            
            return result
        
        finally:
            session.close()
    
    @staticmethod
    def get_user_by_id(user_id: int) -> Optional[Dict[str, Any]]:
        """
        الحصول على معلومات المستخدم بواسطة المعرف
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            معلومات المستخدم إذا وجد، None خلاف ذلك
        """
        session = Session()
        try:
            user = session.query(User).filter_by(id=user_id).first()
            
            if not user:
                return None
            
            return {
                'id': user.id,
                'username': user.username,
                'full_name': user.full_name,
                'email': user.email,
                'role': user.role.value,
                'is_active': user.is_active,
                'created_at': user.created_at,
                'last_login': user.last_login
            }
        
        finally:
            session.close()
    
    @staticmethod
    def get_user_activities(user_id: int) -> List[Dict[str, Any]]:
        """
        الحصول على سجل نشاطات المستخدم
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            قائمة بنشاطات المستخدم
        """
        session = Session()
        try:
            activities = session.query(ActivityLog).filter_by(user_id=user_id).order_by(ActivityLog.timestamp.desc()).all()
            
            result = []
            for activity in activities:
                result.append({
                    'id': activity.id,
                    'action': activity.action,
                    'details': activity.details,
                    'timestamp': activity.timestamp
                })
            
            return result
        
        finally:
            session.close()
