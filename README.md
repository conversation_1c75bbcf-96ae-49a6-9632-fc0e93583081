# نظام إدارة الحوادث المدرسية

تطبيق سطح مكتب لإدارة الحوادث المدرسية والضمان المدرسي، مصمم للعمل على نظام ويندوز 7 وما فوق.

## المميزات الرئيسية

- تسجيل وإدارة الحوادث المدرسية
- نظام ترقيم تلقائي للملفات حسب المستوى التعليمي
- البحث المتقدم في الحوادث
- إنشاء تقارير متنوعة (يومية، أسبوعية، شهرية، سنوية)
- تصدير التقارير إلى PDF وExcel
- نظام النسخ الاحتياطي والاستعادة
- إدارة المستخدمين والصلاحيات
- لوحة معلومات مع إحصائيات

## متطلبات النظام

- نظام ويندوز 7 أو أحدث
- مساحة قرص صلب: 100 ميجابايت على الأقل
- ذاكرة وصول عشوائي (RAM): 2 جيجابايت على الأقل

## التثبيت

### الطريقة 1: تثبيت من ملف التثبيت

1. قم بتنزيل أحدث إصدار من ملف التثبيت من [صفحة الإصدارات](https://github.com/yourusername/school-incidents-manager/releases)
2. قم بتشغيل ملف التثبيت واتبع التعليمات

### الطريقة 2: تشغيل من المصدر

1. تأكد من تثبيت Python 3.7 أو أحدث
2. قم بتنزيل أو استنساخ المشروع
3. افتح موجه الأوامر في مجلد المشروع
4. قم بتثبيت المتطلبات:
   ```
   pip install -r requirements.txt
   ```
5. قم بتشغيل التطبيق:
   ```
   python main.py
   ```

## بدء الاستخدام

1. عند تشغيل التطبيق لأول مرة، سيتم إنشاء قاعدة بيانات جديدة
2. قم بتسجيل الدخول باستخدام اسم المستخدم وكلمة المرور الافتراضية:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`
3. قم بتغيير كلمة المرور الافتراضية من خلال قائمة الإعدادات

## دليل الاستخدام

### تسجيل حادثة جديدة

1. انقر على زر "حادثة جديدة" في الشريط الجانبي
2. أدخل معلومات الحادثة المطلوبة:
   - اسم التلميذ
   - رقم مسار
   - الجنس (ذكر/أنثى)
   - المؤسسة
   - الوسط (حضري/قروي)
   - السلك (ابتدائي، إعدادي، تأهيلي)
   - نوع الحادثة (حادثة مدرسية، حادثة تنقل، حادثة رياضية)
   - مرجع التأمين (اختياري)
   - تاريخ التسوية (اختياري)
   - الحالة (قيد التسوية، تمت التسوية، مرفوض، تم الدفع)
   - ملاحظات (اختياري)
3. انقر على زر "حفظ" لتسجيل الحادثة

### البحث في الحوادث

1. انقر على "قائمة الحوادث" في الشريط الجانبي
2. استخدم حقول البحث المختلفة للعثور على الحوادث المطلوبة
3. يمكنك البحث حسب:
   - اسم التلميذ
   - المؤسسة
   - السلك
   - نوع الحادثة
   - الحالة
   - التاريخ

### إنشاء التقارير

1. انقر على "التقارير" في الشريط الجانبي
2. اختر نوع التقرير (يومي، أسبوعي، شهري، سنوي)
3. حدد المعايير المطلوبة (التاريخ، الفترة)
4. اختر صيغة التقرير (PDF أو Excel)
5. انقر على "إنشاء التقرير"

### النسخ الاحتياطي

1. انقر على "الإعدادات" في الشريط الجانبي
2. اختر "النسخ الاحتياطي"
3. لإنشاء نسخة احتياطية:
   - انقر على "إنشاء نسخة احتياطية"
   - أدخل وصفًا للنسخة الاحتياطية (اختياري)
   - انقر على "حفظ"
4. لاستعادة نسخة احتياطية:
   - اختر النسخة الاحتياطية من القائمة
   - انقر على "استعادة"
   - أكد العملية

### إدارة المستخدمين

1. انقر على "الإعدادات" في الشريط الجانبي
2. اختر "إدارة المستخدمين"
3. لإضافة مستخدم جديد:
   - انقر على "إضافة مستخدم"
   - أدخل معلومات المستخدم
   - حدد دور المستخدم (مدير أو مستخدم)
   - انقر على "حفظ"
4. لتعديل مستخدم:
   - اختر المستخدم من القائمة
   - انقر على "تعديل"
   - قم بتحديث المعلومات
   - انقر على "حفظ"

## الدعم الفني

إذا واجهتك أي مشكلة أو كان لديك أي استفسار، يرجى التواصل معنا عبر:

- البريد الإلكتروني: <EMAIL>
- الهاتف: **********

## الترخيص

هذا البرنامج مرخص بموجب [رخصة MIT](LICENSE).
