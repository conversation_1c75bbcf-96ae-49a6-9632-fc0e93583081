"""
متحكم التقارير - يدير عمليات إنشاء وعرض التقارير
"""
import os
import sys
import datetime
from typing import Optional, List, Dict, Any

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.report import ReportManager
from controllers.auth_controller import AuthController

class ReportController:
    """متحكم التقارير"""
    
    def __init__(self, auth_controller: AuthController):
        """
        تهيئة متحكم التقارير
        
        Args:
            auth_controller: متحكم المصادقة
        """
        self.auth_controller = auth_controller
    
    def create_report(
        self,
        title: str,
        report_type: str,
        data: List[Dict[str, Any]],
        headers: List[str],
        file_format: str,
        incident_id: int = None
    ) -> Optional[Dict[str, Any]]:
        """
        إنشاء تقرير جديد
        
        Args:
            title: عنوان التقرير
            report_type: نوع التقرير
            data: بيانات التقرير
            headers: عناوين الأعمدة
            file_format: صيغة الملف (pdf أو excel)
            incident_id: معرف الحادثة (اختياري)
            
        Returns:
            معلومات التقرير الجديد إذا نجحت العملية، None خلاف ذلك
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return None
        
        # إنشاء التقرير
        report = ReportManager.create_report(
            title=title,
            report_type=report_type,
            data=data,
            headers=headers,
            file_format=file_format,
            incident_id=incident_id,
            user_id=self.auth_controller.current_user.id
        )
        
        if report:
            return ReportManager.get_report_by_id(report.id)
        
        return None
    
    def get_reports(self, report_type: str = None, start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """
        الحصول على قائمة التقارير
        
        Args:
            report_type: نوع التقرير (اختياري)
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            
        Returns:
            قائمة بالتقارير المطابقة للمعايير
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return []
        
        # تحويل تواريخ البداية والنهاية
        start_date_obj = None
        if start_date:
            try:
                start_date_obj = datetime.datetime.strptime(start_date, "%Y/%m/%d")
            except ValueError:
                try:
                    start_date_obj = datetime.datetime.strptime(start_date, "%Y-%m-%d")
                except ValueError:
                    pass
        
        end_date_obj = None
        if end_date:
            try:
                end_date_obj = datetime.datetime.strptime(end_date, "%Y/%m/%d")
                # إضافة يوم كامل للتاريخ النهائي
                end_date_obj = end_date_obj.replace(hour=23, minute=59, second=59)
            except ValueError:
                try:
                    end_date_obj = datetime.datetime.strptime(end_date, "%Y-%m-%d")
                    end_date_obj = end_date_obj.replace(hour=23, minute=59, second=59)
                except ValueError:
                    pass
        
        return ReportManager.get_reports(report_type, start_date_obj, end_date_obj)
    
    def get_report(self, report_id: int) -> Optional[Dict[str, Any]]:
        """
        الحصول على معلومات التقرير
        
        Args:
            report_id: معرف التقرير
            
        Returns:
            معلومات التقرير إذا وجد، None خلاف ذلك
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return None
        
        return ReportManager.get_report_by_id(report_id)
    
    def delete_report(self, report_id: int) -> bool:
        """
        حذف تقرير
        
        Args:
            report_id: معرف التقرير
            
        Returns:
            True إذا نجحت العملية، False خلاف ذلك
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return False
        
        return ReportManager.delete_report(
            report_id=report_id,
            user_id=self.auth_controller.current_user.id
        )
    
    def generate_daily_report(self, date: str) -> Optional[Dict[str, Any]]:
        """
        إنشاء تقرير يومي
        
        Args:
            date: التاريخ
            
        Returns:
            معلومات التقرير الجديد إذا نجحت العملية، None خلاف ذلك
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return None
        
        # تحويل التاريخ
        try:
            date_obj = datetime.datetime.strptime(date, "%Y/%m/%d")
        except ValueError:
            try:
                date_obj = datetime.datetime.strptime(date, "%Y-%m-%d")
            except ValueError:
                return None
        
        # إنشاء التقرير
        report = ReportManager.generate_daily_report(
            date=date_obj,
            user_id=self.auth_controller.current_user.id
        )
        
        if report:
            return ReportManager.get_report_by_id(report.id)
        
        return None
    
    def generate_weekly_report(self, start_date: str) -> Optional[Dict[str, Any]]:
        """
        إنشاء تقرير أسبوعي
        
        Args:
            start_date: تاريخ بداية الأسبوع
            
        Returns:
            معلومات التقرير الجديد إذا نجحت العملية، None خلاف ذلك
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return None
        
        # تحويل التاريخ
        try:
            start_date_obj = datetime.datetime.strptime(start_date, "%Y/%m/%d")
        except ValueError:
            try:
                start_date_obj = datetime.datetime.strptime(start_date, "%Y-%m-%d")
            except ValueError:
                return None
        
        # إنشاء التقرير
        report = ReportManager.generate_weekly_report(
            start_date=start_date_obj,
            user_id=self.auth_controller.current_user.id
        )
        
        if report:
            return ReportManager.get_report_by_id(report.id)
        
        return None
    
    def generate_monthly_report(self, year: int, month: int) -> Optional[Dict[str, Any]]:
        """
        إنشاء تقرير شهري
        
        Args:
            year: السنة
            month: الشهر
            
        Returns:
            معلومات التقرير الجديد إذا نجحت العملية، None خلاف ذلك
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return None
        
        # إنشاء التقرير
        current_user = self.auth_controller.get_current_user()
        report = ReportManager.generate_monthly_report(
            year=year,
            month=month,
            user_id=current_user['id']
        )
        
        if report:
            return ReportManager.get_report_by_id(report.id)
        
        return None
    
    def generate_yearly_report(self, year: int) -> Optional[Dict[str, Any]]:
        """
        إنشاء تقرير سنوي
        
        Args:
            year: السنة
            
        Returns:
            معلومات التقرير الجديد إذا نجحت العملية، None خلاف ذلك
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return None
        
        # إنشاء التقرير
        report = ReportManager.generate_yearly_report(
            year=year,
            user_id=self.auth_controller.current_user.id
        )
        
        if report:
            return ReportManager.get_report_by_id(report.id)
        
        return None
