"""
نموذج قاعدة البيانات - يدير الاتصال بقاعدة البيانات وتعريف الجداول
"""
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Boolean, ForeignKey, Text, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
import datetime
import enum
import os
import sys

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import DB_URI

# إنشاء محرك قاعدة البيانات
engine = create_engine(DB_URI, echo=False)
Base = declarative_base()
Session = sessionmaker(bind=engine)

# تعريف الأنواع المستخدمة في قاعدة البيانات
class Gender(enum.Enum):
    MALE = "ذكر"
    FEMALE = "أنثى"

class Environment(enum.Enum):
    URBAN = "حضري"
    RURAL = "قروي"

class EducationLevel(enum.Enum):
    PRIMARY = "ابتدائي"
    MIDDLE = "إعدادي"
    HIGH = "تأهيلي"

class IncidentType(enum.Enum):
    SCHOOL = "حادثة مدرسية"
    TRANSPORT = "حادثة تنقل"
    SPORT = "حادثة رياضية"

class IncidentStatus(enum.Enum):
    PENDING = "قيد التسوية"
    RESOLVED = "تمت التسوية"
    REJECTED = "مرفوض"
    PAID = "تم الدفع"

class UserRole(enum.Enum):
    ADMIN = "مدير"
    USER = "مستخدم"

# تعريف جداول قاعدة البيانات
class User(Base):
    """نموذج المستخدم"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    password_hash = Column(String(128), nullable=False)
    full_name = Column(String(100), nullable=False)
    email = Column(String(100), nullable=True)
    role = Column(Enum(UserRole), default=UserRole.USER)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.datetime.now)
    last_login = Column(DateTime, nullable=True)
    
    # العلاقات
    activities = relationship("ActivityLog", back_populates="user")
    
    def __repr__(self):
        return f"<User {self.username}>"

class Incident(Base):
    """نموذج الحادثة"""
    __tablename__ = 'incidents'
    
    id = Column(Integer, primary_key=True)
    file_number = Column(String(20), unique=True, nullable=False)
    student_name = Column(String(100), nullable=False)
    massar_number = Column(String(20), nullable=True)
    gender = Column(Enum(Gender), nullable=False)
    institution = Column(String(100), nullable=False)
    environment = Column(Enum(Environment), nullable=False)
    education_level = Column(Enum(EducationLevel), nullable=False)
    incident_type = Column(Enum(IncidentType), nullable=False)
    insurance_reference = Column(String(50), nullable=True)
    settlement_date = Column(DateTime, nullable=True)
    status = Column(Enum(IncidentStatus), default=IncidentStatus.PENDING)
    created_at = Column(DateTime, default=datetime.datetime.now)
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    notes = Column(Text, nullable=True)
    created_by = Column(Integer, ForeignKey('users.id'))
    
    # العلاقات
    reports = relationship("Report", back_populates="incident")
    creator = relationship("User")
    
    def __repr__(self):
        return f"<Incident {self.file_number}>"

class Report(Base):
    """نموذج التقرير"""
    __tablename__ = 'reports'
    
    id = Column(Integer, primary_key=True)
    title = Column(String(100), nullable=False)
    report_type = Column(String(50), nullable=False)
    file_path = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.now)
    incident_id = Column(Integer, ForeignKey('incidents.id'), nullable=True)
    created_by = Column(Integer, ForeignKey('users.id'))
    
    # العلاقات
    incident = relationship("Incident", back_populates="reports")
    creator = relationship("User")
    
    def __repr__(self):
        return f"<Report {self.title}>"

class ActivityLog(Base):
    """نموذج سجل النشاطات"""
    __tablename__ = 'activity_logs'
    
    id = Column(Integer, primary_key=True)
    action = Column(String(100), nullable=False)
    details = Column(Text, nullable=True)
    timestamp = Column(DateTime, default=datetime.datetime.now)
    user_id = Column(Integer, ForeignKey('users.id'))
    
    # العلاقات
    user = relationship("User", back_populates="activities")
    
    def __repr__(self):
        return f"<ActivityLog {self.action}>"

class Setting(Base):
    """نموذج الإعدادات"""
    __tablename__ = 'settings'
    
    id = Column(Integer, primary_key=True)
    key = Column(String(50), unique=True, nullable=False)
    value = Column(String(255), nullable=True)
    description = Column(String(255), nullable=True)
    
    def __repr__(self):
        return f"<Setting {self.key}>"

def init_db():
    """تهيئة قاعدة البيانات وإنشاء الجداول"""
    Base.metadata.create_all(engine)
    
    # إنشاء جلسة
    session = Session()
    
    # التحقق من وجود مستخدم مدير افتراضي
    admin = session.query(User).filter_by(username='admin').first()
    if not admin:
        # إنشاء مستخدم مدير افتراضي
        from utils.helpers import hash_password
        admin = User(
            username='admin',
            password_hash=hash_password('admin123'),
            full_name='مدير النظام',
            role=UserRole.ADMIN
        )
        session.add(admin)
    
    # إضافة إعدادات افتراضية
    default_settings = [
        {'key': 'primary_counter', 'value': '0', 'description': 'عداد ملفات الابتدائي'},
        {'key': 'middle_counter', 'value': '0', 'description': 'عداد ملفات الإعدادي'},
        {'key': 'high_counter', 'value': '0', 'description': 'عداد ملفات التأهيلي'},
        {'key': 'current_year', 'value': str(datetime.datetime.now().year % 100), 'description': 'السنة الحالية'},
    ]
    
    for setting in default_settings:
        existing = session.query(Setting).filter_by(key=setting['key']).first()
        if not existing:
            session.add(Setting(**setting))
    
    # حفظ التغييرات
    session.commit()
    session.close()

if __name__ == "__main__":
    # إذا تم تشغيل هذا الملف مباشرة، قم بتهيئة قاعدة البيانات
    init_db()
    print("تم تهيئة قاعدة البيانات بنجاح!")
