/* أنماط CSS للتطبيق */

/* النمط العام */
QWidget {
    font-family: Arial;
    font-size: 10pt;
    background-color: #f5f5f5;
}

/* العنوان الرئيسي */
QLabel#titleLabel {
    font-size: 18pt;
    font-weight: bold;
    color: #2c3e50;
    padding: 10px;
}

/* الأزرار */
QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    min-width: 100px;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed {
    background-color: #1c6ea4;
}

QPushButton:disabled {
    background-color: #bdc3c7;
    color: #7f8c8d;
}

/* أزرار الإجراءات */
QPushButton#actionButton {
    background-color: #2ecc71;
}

QPushButton#actionButton:hover {
    background-color: #27ae60;
}

QPushButton#actionButton:pressed {
    background-color: #1e8449;
}

/* أزرار الحذف */
QPushButton#deleteButton {
    background-color: #e74c3c;
}

QPushButton#deleteButton:hover {
    background-color: #c0392b;
}

QPushButton#deleteButton:pressed {
    background-color: #a93226;
}

/* حقول الإدخال */
QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox {
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    padding: 6px;
    background-color: white;
}

QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus, QSpinBox:focus {
    border: 1px solid #3498db;
}

/* القوائم المنسدلة */
QComboBox {
    padding-right: 20px;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #bdc3c7;
}

QComboBox::down-arrow {
    image: url(resources/icons/dropdown.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    border: 1px solid #bdc3c7;
    selection-background-color: #3498db;
    selection-color: white;
}

/* الجداول - تصميم حديث */
QTableView, QTableWidget {
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    gridline-color: #f1f3f4;
    selection-background-color: #3498db;
    selection-color: white;
    alternate-background-color: #f8f9fa;
    background-color: white;
    font-size: 9pt;
}

QTableView::item, QTableWidget::item {
    padding: 5px;
}

QHeaderView::section {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #5d6d7e, stop:1 #34495e);
    color: white;
    padding: 12px 8px;
    border: none;
    border-right: 1px solid #2c3e50;
    font-weight: 600;
    font-size: 9pt;
    text-align: center;
}

QHeaderView::section:first {
    border-top-left-radius: 8px;
}

QHeaderView::section:last {
    border-top-right-radius: 8px;
    border-right: none;
}

QHeaderView::section:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #6c7b7f, stop:1 #3e5367);
}

/* محاذاة أيقونات الإجراءات في الجداول */
QTableWidget QWidget {
    text-align: center;
    qproperty-alignment: AlignCenter;
    border: none;
    background: transparent;
}

/* أزرار الإجراءات في الجداول - تصميم حديث */
QTableWidget QPushButton {
    min-width: 70px;
    max-width: 90px;
    min-height: 28px;
    padding: 6px 12px;
    margin: 3px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    font-size: 9pt;
    qproperty-alignment: AlignCenter;
    color: white;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #3498db, stop:1 #2980b9);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

QTableWidget QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #5dade2, stop:1 #3498db);
    transform: translateY(-1px);
}

QTableWidget QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #2980b9, stop:1 #1f618d);
    transform: translateY(0px);
}

/* أزرار التعديل - لون أخضر */
QTableWidget QPushButton[text="تعديل"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #27ae60, stop:1 #229954);
}

QTableWidget QPushButton[text="تعديل"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #58d68d, stop:1 #27ae60);
}

QTableWidget QPushButton[text="تعديل"]:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #229954, stop:1 #1e8449);
}

/* أزرار الحذف - لون أحمر */
QTableWidget QPushButton[text="حذف"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e74c3c, stop:1 #c0392b);
}

QTableWidget QPushButton[text="حذف"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ec7063, stop:1 #e74c3c);
}

QTableWidget QPushButton[text="حذف"]:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #c0392b, stop:1 #a93226);
}

/* أزرار النشاطات - لون بنفسجي */
QTableWidget QPushButton[text="النشاطات"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #8e44ad, stop:1 #7d3c98);
}

QTableWidget QPushButton[text="النشاطات"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #a569bd, stop:1 #8e44ad);
}

QTableWidget QPushButton[text="النشاطات"]:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #7d3c98, stop:1 #6c3483);
}

/* أزرار فتح - لون برتقالي */
QTableWidget QPushButton[text="فتح"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f39c12, stop:1 #e67e22);
}

QTableWidget QPushButton[text="فتح"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f7dc6f, stop:1 #f39c12);
}

QTableWidget QPushButton[text="فتح"]:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e67e22, stop:1 #d68910);
}

/* تأثيرات إضافية للأزرار الحديثة */
QTableWidget QPushButton {
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

QTableWidget QPushButton:focus {
    outline: 2px solid rgba(52, 152, 219, 0.5);
    outline-offset: 2px;
}

/* تحسين مظهر الأزرار مع الأيقونات */
QTableWidget QPushButton[text*="✏️"] {
    font-size: 9pt;
    letter-spacing: 0.5px;
}

QTableWidget QPushButton[text*="🗑️"] {
    font-size: 9pt;
    letter-spacing: 0.5px;
}

QTableWidget QPushButton[text*="📊"] {
    font-size: 9pt;
    letter-spacing: 0.5px;
}

QTableWidget QPushButton[text*="📂"] {
    font-size: 9pt;
    letter-spacing: 0.5px;
}

/* تأثير الظل للأزرار */
QTableWidget QPushButton {
    border: 1px solid rgba(0,0,0,0.1);
}

QTableWidget QPushButton:hover {
    border: 1px solid rgba(0,0,0,0.2);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* تحسين التباعد والمحاذاة */
QTableWidget QWidget[objectName="actions_widget"] {
    spacing: 8px;
}

/* تحسين مظهر الصفوف */
QTableWidget::item {
    border-bottom: 1px solid #f1f3f4;
    padding: 10px 8px;
}

QTableWidget::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #5dade2, stop:1 #3498db);
    color: white;
    border-bottom: 1px solid #2980b9;
}

QTableWidget::item:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

/* تحسين الحدود العامة للجدول */
QTableWidget {
    border-collapse: collapse;
    show-decoration-selected: 0;
}

/* إضافة تأثير الظل للجدول */
QTableWidget {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* تحسين مظهر شريط التمرير */
QTableWidget QScrollBar:vertical {
    background-color: #f8f9fa;
    width: 12px;
    border-radius: 6px;
    margin: 0px;
}

QTableWidget QScrollBar::handle:vertical {
    background-color: #bdc3c7;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QTableWidget QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

QTableWidget QScrollBar::add-line:vertical,
QTableWidget QScrollBar::sub-line:vertical {
    height: 0px;
}

QTableWidget QScrollBar::add-page:vertical,
QTableWidget QScrollBar::sub-page:vertical {
    background: transparent;
}

/* ضبط ارتفاع صفوف الجدول لضمان المحاذاة العمودية */
QTableWidget::item {
    padding: 8px 5px;
    text-align: center;
    vertical-align: middle;
}

/* محاذاة عمودية للخلايا التي تحتوي على ويدجت */
QTableWidget {
    gridline-color: #ecf0f1;
}

/* ضبط محاذاة الويدجت داخل خلايا الجدول */
QTableWidget > QWidget {
    margin: 0px;
    padding: 0px;
    border: none;
    background: transparent;
    qproperty-alignment: AlignCenter;
}

/* ضبط محاذاة عمودية مثالية للخلايا */
QTableWidget::item {
    border: none;
    outline: none;
}

/* إزالة الحدود من الويدجت المحيط بالأزرار */
QTableWidget QWidget[objectName="actions_widget"] {
    border: none;
    background: transparent;
    margin: 0px;
    padding: 0px;
    qproperty-alignment: AlignCenter;
}

/* ضبط المحاذاة العمودية للأزرار في الجداول */
QTableWidget QWidget {
    qproperty-alignment: 'AlignCenter | AlignVCenter';
}

/* ضبط محاذاة الخلايا */
QTableWidget::item {
    text-align: center;
    vertical-align: middle;
    padding: 0px;
}

/* ضبط ارتفاع الصفوف لضمان المحاذاة */
QTableWidget {
    gridline-color: #ecf0f1;
    show-decoration-selected: 1;
}

/* إزالة أي هوامش من ويدجت الأزرار */
QTableWidget QWidget[objectName="actions_widget"] {
    margin: 0px;
    padding: 0px;
    qproperty-alignment: 'AlignCenter | AlignVCenter';
}

/* ضبط المحاذاة العمودية للصفوف */
QTableWidget {
    show-decoration-selected: 1;
}

QTableWidget::item:selected {
    background-color: #3498db;
    color: white;
}

/* ضبط محاذاة عمودية شاملة لجميع الخلايا */
QTableWidget::item {
    text-align: center;
    vertical-align: middle;
}

/* إزالة أي حدود إضافية من الويدجت */
QTableWidget QWidget {
    outline: none;
    border: 0px;
}

/* ضبط ارتفاع الصفوف بشكل عام */
QTableWidget {
    gridline-color: #ecf0f1;
    alternate-background-color: #f8f9fa;
}

/* القوائم */
QListView, QListWidget {
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    background-color: white;
}

QListView::item, QListWidget::item {
    padding: 5px;
}

QListView::item:selected, QListWidget::item:selected {
    background-color: #3498db;
    color: white;
}

/* شريط التمرير */
QScrollBar:vertical {
    border: none;
    background-color: #ecf0f1;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #bdc3c7;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #ecf0f1;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #bdc3c7;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #95a5a6;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* علامات التبويب */
QTabWidget::pane {
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    top: -1px;
}

QTabBar::tab {
    background-color: #ecf0f1;
    border: 1px solid #bdc3c7;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 8px 12px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: white;
    border-bottom: 1px solid white;
}

QTabBar::tab:hover:!selected {
    background-color: #d6dbdf;
}

/* مربعات الاختيار */
QCheckBox {
    spacing: 5px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
}

QCheckBox::indicator:unchecked {
    border: 1px solid #bdc3c7;
    background-color: white;
    border-radius: 2px;
}

QCheckBox::indicator:checked {
    border: 1px solid #3498db;
    background-color: #3498db;
    border-radius: 2px;
    image: url(resources/icons/check.png);
}

/* أزرار الراديو */
QRadioButton {
    spacing: 5px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
}

QRadioButton::indicator:unchecked {
    border: 1px solid #bdc3c7;
    background-color: white;
    border-radius: 8px;
}

QRadioButton::indicator:checked {
    border: 1px solid #3498db;
    background-color: white;
    border-radius: 8px;
}

QRadioButton::indicator:checked::before {
    content: "";
    display: block;
    width: 8px;
    height: 8px;
    background-color: #3498db;
    border-radius: 4px;
    margin: 3px;
}

/* شريط الحالة */
QStatusBar {
    background-color: #34495e;
    color: white;
    padding: 5px;
}

/* شريط القوائم */
QMenuBar {
    background-color: #34495e;
    color: white;
}

QMenuBar::item {
    padding: 5px 10px;
    background-color: transparent;
}

QMenuBar::item:selected {
    background-color: #2c3e50;
}

QMenu {
    background-color: white;
    border: 1px solid #bdc3c7;
}

QMenu::item {
    padding: 5px 30px 5px 20px;
}

QMenu::item:selected {
    background-color: #3498db;
    color: white;
}

/* شريط الأدوات */
QToolBar {
    background-color: #34495e;
    border: none;
    spacing: 5px;
    padding: 5px;
}

QToolButton {
    background-color: transparent;
    border: none;
    border-radius: 4px;
    padding: 5px;
}

QToolButton:hover {
    background-color: #2c3e50;
}

QToolButton:pressed {
    background-color: #1c2833;
}

/* مربع الحوار */
QDialog {
    background-color: #f5f5f5;
}

QDialog QLabel#titleLabel {
    font-size: 14pt;
    font-weight: bold;
    color: #2c3e50;
    padding: 10px;
}

/* مربع الرسائل */
QMessageBox {
    background-color: #f5f5f5;
}

QMessageBox QLabel {
    color: #2c3e50;
}

/* لوحة المعلومات */
QFrame#dashboardFrame {
    background-color: white;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
}

QLabel#dashboardTitle {
    font-size: 14pt;
    font-weight: bold;
    color: #2c3e50;
}

QLabel#dashboardValue {
    font-size: 24pt;
    font-weight: bold;
    color: #3498db;
}

QLabel#dashboardDescription {
    color: #7f8c8d;
}

/* نموذج الحادثة */
QGroupBox {
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    margin-top: 10px;
    padding-top: 15px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 5px;
    background-color: #f5f5f5;
    color: #2c3e50;
    font-weight: bold;
}

/* شاشة تسجيل الدخول */
QWidget#loginWidget {
    background-color: white;
}

QLabel#loginTitle {
    font-size: 18pt;
    font-weight: bold;
    color: #2c3e50;
}

QLineEdit#usernameInput, QLineEdit#passwordInput {
    padding: 10px;
    font-size: 12pt;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
}

QPushButton#loginButton {
    padding: 10px;
    font-size: 12pt;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
}

QPushButton#loginButton:hover {
    background-color: #2980b9;
}

/* شاشة الإعدادات */
QWidget#settingsWidget {
    background-color: white;
}

QLabel#settingsTitle {
    font-size: 14pt;
    font-weight: bold;
    color: #2c3e50;
}

/* شاشة التقارير */
QWidget#reportsWidget {
    background-color: white;
}

QLabel#reportsTitle {
    font-size: 14pt;
    font-weight: bold;
    color: #2c3e50;
}


