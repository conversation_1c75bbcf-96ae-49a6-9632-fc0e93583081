#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار بسيط للتطبيق
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import main

if __name__ == "__main__":
    print("🚀 بدء تشغيل التطبيق...")
    print("📋 اسم المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("=" * 50)
    
    try:
        sys.exit(main())
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")
