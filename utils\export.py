"""
وظائف تصدير البيانات إلى PDF وExcel
"""
import os
import sys
from datetime import datetime
from typing import List, Dict, Any

from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import REPORTS_DIR

# تسجيل الخطوط العربية
try:
    pdfmetrics.registerFont(TTFont('Arabic', os.path.join(os.path.dirname(__file__), '..', 'resources', 'fonts', 'Arial.ttf')))
except:
    # إذا لم يتم العثور على الخط، استخدم الخط الافتراضي
    pass

def export_to_pdf(data: List[Dict[str, Any]], headers: List[str], title: str, filename: str) -> str:
    """
    تصدير البيانات إلى ملف PDF
    
    Args:
        data: قائمة بالبيانات المراد تصديرها
        headers: عناوين الأعمدة
        title: عنوان التقرير
        filename: اسم الملف (بدون امتداد)
        
    Returns:
        مسار الملف المنشأ
    """
    # إنشاء مجلد التقارير إذا لم يكن موجودًا
    os.makedirs(REPORTS_DIR, exist_ok=True)
    
    # إنشاء اسم الملف الكامل
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    full_filename = f"{filename}_{timestamp}.pdf"
    file_path = os.path.join(REPORTS_DIR, full_filename)
    
    # إنشاء مستند PDF
    doc = SimpleDocTemplate(
        file_path,
        pagesize=A4,
        rightMargin=30,
        leftMargin=30,
        topMargin=30,
        bottomMargin=30
    )
    
    # إنشاء قائمة العناصر
    elements = []
    
    # إنشاء أنماط النص
    styles = getSampleStyleSheet()
    
    # إنشاء نمط للنص العربي
    arabic_style = ParagraphStyle(
        'Arabic',
        parent=styles['Heading1'],
        fontName='Arabic',
        alignment=1,  # وسط
        fontSize=16,
        leading=20
    )
    
    # إضافة العنوان
    elements.append(Paragraph(title, arabic_style))
    elements.append(Spacer(1, 20))
    
    # إضافة التاريخ
    date_style = ParagraphStyle(
        'Date',
        parent=styles['Normal'],
        fontName='Arabic',
        alignment=2,  # يمين
        fontSize=10
    )
    elements.append(Paragraph(f"تاريخ التقرير: {datetime.now().strftime('%Y/%m/%d')}", date_style))
    elements.append(Spacer(1, 20))
    
    # إعداد البيانات للجدول
    table_data = [headers]
    for row in data:
        table_row = []
        for header in headers:
            table_row.append(str(row.get(header, "")))
        table_data.append(table_row)
    
    # إنشاء الجدول
    table = Table(table_data, repeatRows=1)
    
    # تنسيق الجدول
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Arabic'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
    ]))
    
    # إضافة الجدول إلى المستند
    elements.append(table)
    
    # بناء المستند
    doc.build(elements)
    
    return file_path

def export_to_excel(data: List[Dict[str, Any]], headers: List[str], title: str, filename: str) -> str:
    """
    تصدير البيانات إلى ملف Excel
    
    Args:
        data: قائمة بالبيانات المراد تصديرها
        headers: عناوين الأعمدة
        title: عنوان التقرير
        filename: اسم الملف (بدون امتداد)
        
    Returns:
        مسار الملف المنشأ
    """
    # إنشاء مجلد التقارير إذا لم يكن موجودًا
    os.makedirs(REPORTS_DIR, exist_ok=True)
    
    # إنشاء اسم الملف الكامل
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    full_filename = f"{filename}_{timestamp}.xlsx"
    file_path = os.path.join(REPORTS_DIR, full_filename)
    
    # إنشاء مصنف Excel جديد
    wb = Workbook()
    ws = wb.active
    ws.title = title
    
    # إضافة العنوان
    ws.merge_cells('A1:E1')
    title_cell = ws['A1']
    title_cell.value = title
    title_cell.font = Font(size=16, bold=True)
    title_cell.alignment = Alignment(horizontal='center')
    
    # إضافة التاريخ
    ws['A2'] = f"تاريخ التقرير: {datetime.now().strftime('%Y/%m/%d')}"
    ws['A2'].alignment = Alignment(horizontal='right')
    
    # إضافة عناوين الأعمدة
    for col_idx, header in enumerate(headers, 1):
        cell = ws.cell(row=4, column=col_idx)
        cell.value = header
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
        cell.alignment = Alignment(horizontal='center')
        
        # تعيين عرض العمود
        ws.column_dimensions[chr(64 + col_idx)].width = 20
    
    # إضافة البيانات
    for row_idx, row_data in enumerate(data, 5):
        for col_idx, header in enumerate(headers, 1):
            cell = ws.cell(row=row_idx, column=col_idx)
            cell.value = str(row_data.get(header, ""))
            cell.alignment = Alignment(horizontal='center')
    
    # إضافة حدود للجدول
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    for row in ws.iter_rows(min_row=4, max_row=len(data) + 4, min_col=1, max_col=len(headers)):
        for cell in row:
            cell.border = thin_border
    
    # حفظ المصنف
    wb.save(file_path)
    
    return file_path
