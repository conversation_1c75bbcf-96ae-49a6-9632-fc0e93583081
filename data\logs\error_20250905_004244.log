تاريخ الخطأ: 2025/09/05 00:42:44
نوع الخطأ: DetachedInstanceError
رسالة الخطأ: Instance <User at 0x1d06782e5d0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/14/bhk3)

تفاصيل الخطأ:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\daman5\main.py", line 127, in on_login_success
    main_window = MainWindow(
        auth_controller=auth_controller,
    ...<2 lines>...
        backup_controller=backup_controller
    )
  File "C:\Users\<USER>\Desktop\daman5\views\main_window.py", line 65, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\daman5\views\main_window.py", line 123, in setup_ui
    self.add_menu_items()
    ~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\daman5\views\main_window.py", line 212, in add_menu_items
    if self.auth_controller.is_admin():
       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\daman5\controllers\auth_controller.py", line 61, in is_admin
    return self.current_user.role == UserRole.ADMIN
           ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\attributes.py", line 487, in __get__
    return self.impl.get(state, dict_)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\attributes.py", line 959, in get
    value = self._fire_loader_callables(state, key, passive)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\attributes.py", line 990, in _fire_loader_callables
    return state._load_expired(state, passive)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\state.py", line 712, in _load_expired
    self.manager.expired_attribute_loader(self, toload, passive)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\loading.py", line 1369, in load_scalar_attributes
    raise orm_exc.DetachedInstanceError(
    ...<2 lines>...
    )
sqlalchemy.orm.exc.DetachedInstanceError: Instance <User at 0x1d06782e5d0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/14/bhk3)
