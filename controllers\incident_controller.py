"""
متحكم الحوادث - يدير عمليات الحوادث المدرسية
"""
import os
import sys
import datetime
from typing import Optional, List, Dict, Any, Tuple

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import Gender, Environment, EducationLevel, IncidentType, IncidentStatus
from models.incident import IncidentManager
from controllers.auth_controller import AuthController

class IncidentController:
    """متحكم الحوادث"""
    
    def __init__(self, auth_controller: AuthController):
        """
        تهيئة متحكم الحوادث
        
        Args:
            auth_controller: متحكم المصادقة
        """
        self.auth_controller = auth_controller
    
    def create_incident(
        self,
        student_name: str,
        massar_number: str,
        gender: str,
        institution: str,
        environment: str,
        education_level: str,
        incident_type: str,
        insurance_reference: str = None,
        settlement_date: str = None,
        status: str = None,
        notes: str = None,
        file_number: str = None
    ) -> Optional[Dict[str, Any]]:
        """
        إنشاء حادثة جديدة
        
        Args:
            student_name: اسم التلميذ
            massar_number: رقم مسار
            gender: الجنس (ذكر/أنثى)
            institution: المؤسسة
            environment: الوسط (حضري/قروي)
            education_level: المستوى التعليمي (ابتدائي/إعدادي/تأهيلي)
            incident_type: نوع الحادثة (حادثة مدرسية/حادثة تنقل/حادثة رياضية)
            insurance_reference: مرجع التأمين (اختياري)
            settlement_date: تاريخ التسوية (اختياري)
            status: حالة الحادثة (اختياري)
            notes: ملاحظات (اختياري)
            file_number: رقم الملف (اختياري)
            
        Returns:
            معلومات الحادثة الجديدة إذا نجحت العملية، None خلاف ذلك
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return None
        
        # تحويل القيم النصية إلى الأنواع المناسبة
        try:
            gender_enum = Gender.MALE if gender == "ذكر" else Gender.FEMALE
            environment_enum = Environment.URBAN if environment == "حضري" else Environment.RURAL
            
            if education_level == "ابتدائي":
                education_level_enum = EducationLevel.PRIMARY
            elif education_level == "إعدادي":
                education_level_enum = EducationLevel.MIDDLE
            else:
                education_level_enum = EducationLevel.HIGH
            
            if incident_type == "حادثة مدرسية":
                incident_type_enum = IncidentType.SCHOOL
            elif incident_type == "حادثة تنقل":
                incident_type_enum = IncidentType.TRANSPORT
            else:
                incident_type_enum = IncidentType.SPORT
            
            if status:
                if status == "قيد التسوية":
                    status_enum = IncidentStatus.PENDING
                elif status == "تمت التسوية":
                    status_enum = IncidentStatus.RESOLVED
                elif status == "مرفوض":
                    status_enum = IncidentStatus.REJECTED
                else:
                    status_enum = IncidentStatus.PAID
            else:
                status_enum = IncidentStatus.PENDING
            
            # تحويل تاريخ التسوية
            settlement_date_obj = None
            if settlement_date:
                try:
                    settlement_date_obj = datetime.datetime.strptime(settlement_date, "%Y/%m/%d")
                except ValueError:
                    try:
                        settlement_date_obj = datetime.datetime.strptime(settlement_date, "%Y-%m-%d")
                    except ValueError:
                        pass
            
            # إنشاء الحادثة
            incident = IncidentManager.create_incident(
                student_name=student_name,
                massar_number=massar_number,
                gender=gender_enum,
                institution=institution,
                environment=environment_enum,
                education_level=education_level_enum,
                incident_type=incident_type_enum,
                insurance_reference=insurance_reference,
                settlement_date=settlement_date_obj,
                status=status_enum,
                notes=notes,
                user_id=self.auth_controller.current_user.id,
                file_number=file_number
            )
            
            if incident:
                return IncidentManager.get_incident_by_id(incident.id)
            
            return None
        
        except Exception as e:
            print(f"خطأ أثناء إنشاء الحادثة: {e}")
            return None
    
    def update_incident(
        self,
        incident_id: int,
        student_name: str = None,
        massar_number: str = None,
        gender: str = None,
        institution: str = None,
        environment: str = None,
        education_level: str = None,
        incident_type: str = None,
        insurance_reference: str = None,
        settlement_date: str = None,
        status: str = None,
        notes: str = None
    ) -> bool:
        """
        تحديث بيانات الحادثة
        
        Args:
            incident_id: معرف الحادثة
            student_name: اسم التلميذ (اختياري)
            massar_number: رقم مسار (اختياري)
            gender: الجنس (اختياري)
            institution: المؤسسة (اختياري)
            environment: الوسط (اختياري)
            education_level: المستوى التعليمي (اختياري)
            incident_type: نوع الحادثة (اختياري)
            insurance_reference: مرجع التأمين (اختياري)
            settlement_date: تاريخ التسوية (اختياري)
            status: حالة الحادثة (اختياري)
            notes: ملاحظات (اختياري)
            
        Returns:
            True إذا نجحت العملية، False خلاف ذلك
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return False
        
        # تحويل القيم النصية إلى الأنواع المناسبة
        try:
            gender_enum = None
            if gender:
                gender_enum = Gender.MALE if gender == "ذكر" else Gender.FEMALE
            
            environment_enum = None
            if environment:
                environment_enum = Environment.URBAN if environment == "حضري" else Environment.RURAL
            
            education_level_enum = None
            if education_level:
                if education_level == "ابتدائي":
                    education_level_enum = EducationLevel.PRIMARY
                elif education_level == "إعدادي":
                    education_level_enum = EducationLevel.MIDDLE
                else:
                    education_level_enum = EducationLevel.HIGH
            
            incident_type_enum = None
            if incident_type:
                if incident_type == "حادثة مدرسية":
                    incident_type_enum = IncidentType.SCHOOL
                elif incident_type == "حادثة تنقل":
                    incident_type_enum = IncidentType.TRANSPORT
                else:
                    incident_type_enum = IncidentType.SPORT
            
            status_enum = None
            if status:
                if status == "قيد التسوية":
                    status_enum = IncidentStatus.PENDING
                elif status == "تمت التسوية":
                    status_enum = IncidentStatus.RESOLVED
                elif status == "مرفوض":
                    status_enum = IncidentStatus.REJECTED
                else:
                    status_enum = IncidentStatus.PAID
            
            # تحويل تاريخ التسوية
            settlement_date_obj = None
            if settlement_date:
                try:
                    settlement_date_obj = datetime.datetime.strptime(settlement_date, "%Y/%m/%d")
                except ValueError:
                    try:
                        settlement_date_obj = datetime.datetime.strptime(settlement_date, "%Y-%m-%d")
                    except ValueError:
                        pass
            
            # تحديث الحادثة
            return IncidentManager.update_incident(
                incident_id=incident_id,
                student_name=student_name,
                massar_number=massar_number,
                gender=gender_enum,
                institution=institution,
                environment=environment_enum,
                education_level=education_level_enum,
                incident_type=incident_type_enum,
                insurance_reference=insurance_reference,
                settlement_date=settlement_date_obj,
                status=status_enum,
                notes=notes,
                user_id=self.auth_controller.current_user.id
            )
        
        except Exception as e:
            print(f"خطأ أثناء تحديث الحادثة: {e}")
            return False
    
    def delete_incident(self, incident_id: int) -> bool:
        """
        حذف حادثة
        
        Args:
            incident_id: معرف الحادثة
            
        Returns:
            True إذا نجحت العملية، False خلاف ذلك
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return False
        
        # المستخدم العادي لا يمكنه حذف الحوادث
        if not self.auth_controller.is_admin():
            return False
        
        return IncidentManager.delete_incident(
            incident_id=incident_id,
            user_id=self.auth_controller.current_user.id
        )
    
    def get_incident(self, incident_id: int) -> Optional[Dict[str, Any]]:
        """
        الحصول على معلومات الحادثة
        
        Args:
            incident_id: معرف الحادثة
            
        Returns:
            معلومات الحادثة إذا وجدت، None خلاف ذلك
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return None
        
        return IncidentManager.get_incident_by_id(incident_id)
    
    def get_incident_by_file_number(self, file_number: str) -> Optional[Dict[str, Any]]:
        """
        الحصول على معلومات الحادثة بواسطة رقم الملف
        
        Args:
            file_number: رقم الملف
            
        Returns:
            معلومات الحادثة إذا وجدت، None خلاف ذلك
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return None
        
        return IncidentManager.get_incident_by_file_number(file_number)
    
    def search_incidents(
        self,
        student_name: str = None,
        institution: str = None,
        education_level: str = None,
        incident_type: str = None,
        status: str = None,
        gender: str = None,
        environment: str = None,
        start_date: str = None,
        end_date: str = None,
        page: int = 1,
        per_page: int = 20
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        البحث عن الحوادث حسب معايير مختلفة
        
        Args:
            student_name: اسم التلميذ (اختياري)
            institution: المؤسسة (اختياري)
            education_level: المستوى التعليمي (اختياري)
            incident_type: نوع الحادثة (اختياري)
            status: حالة الحادثة (اختياري)
            gender: الجنس (اختياري)
            environment: الوسط (اختياري)
            start_date: تاريخ البداية (اختياري)
            end_date: تاريخ النهاية (اختياري)
            page: رقم الصفحة
            per_page: عدد العناصر في الصفحة
            
        Returns:
            قائمة بالحوادث المطابقة للمعايير وإجمالي عدد النتائج
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return [], 0
        
        # تحويل القيم النصية إلى الأنواع المناسبة
        try:
            gender_enum = None
            if gender:
                gender_enum = Gender.MALE if gender == "ذكر" else Gender.FEMALE
            
            environment_enum = None
            if environment:
                environment_enum = Environment.URBAN if environment == "حضري" else Environment.RURAL
            
            education_level_enum = None
            if education_level:
                if education_level == "ابتدائي":
                    education_level_enum = EducationLevel.PRIMARY
                elif education_level == "إعدادي":
                    education_level_enum = EducationLevel.MIDDLE
                elif education_level == "تأهيلي":
                    education_level_enum = EducationLevel.HIGH
            
            incident_type_enum = None
            if incident_type:
                if incident_type == "حادثة مدرسية":
                    incident_type_enum = IncidentType.SCHOOL
                elif incident_type == "حادثة تنقل":
                    incident_type_enum = IncidentType.TRANSPORT
                elif incident_type == "حادثة رياضية":
                    incident_type_enum = IncidentType.SPORT
            
            status_enum = None
            if status:
                if status == "قيد التسوية":
                    status_enum = IncidentStatus.PENDING
                elif status == "تمت التسوية":
                    status_enum = IncidentStatus.RESOLVED
                elif status == "مرفوض":
                    status_enum = IncidentStatus.REJECTED
                elif status == "تم الدفع":
                    status_enum = IncidentStatus.PAID
            
            # تحويل تواريخ البداية والنهاية
            start_date_obj = None
            if start_date:
                try:
                    start_date_obj = datetime.datetime.strptime(start_date, "%Y/%m/%d")
                except ValueError:
                    try:
                        start_date_obj = datetime.datetime.strptime(start_date, "%Y-%m-%d")
                    except ValueError:
                        pass
            
            end_date_obj = None
            if end_date:
                try:
                    end_date_obj = datetime.datetime.strptime(end_date, "%Y/%m/%d")
                    # إضافة يوم كامل للتاريخ النهائي
                    end_date_obj = end_date_obj.replace(hour=23, minute=59, second=59)
                except ValueError:
                    try:
                        end_date_obj = datetime.datetime.strptime(end_date, "%Y-%m-%d")
                        end_date_obj = end_date_obj.replace(hour=23, minute=59, second=59)
                    except ValueError:
                        pass
            
            # البحث عن الحوادث
            return IncidentManager.search_incidents(
                student_name=student_name,
                institution=institution,
                education_level=education_level_enum,
                incident_type=incident_type_enum,
                status=status_enum,
                gender=gender_enum,
                environment=environment_enum,
                start_date=start_date_obj,
                end_date=end_date_obj,
                page=page,
                per_page=per_page
            )
        
        except Exception as e:
            print(f"خطأ أثناء البحث عن الحوادث: {e}")
            return [], 0
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات الحوادث
        
        Returns:
            قاموس يحتوي على إحصائيات مختلفة
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return {}
        
        return IncidentManager.get_statistics()
