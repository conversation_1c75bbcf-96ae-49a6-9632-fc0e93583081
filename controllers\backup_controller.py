"""
متحكم النسخ الاحتياطي - يدير عمليات النسخ الاحتياطي واستعادة البيانات
"""
import os
import sys
from typing import List, Dict, Any

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.backup import BackupManager
from models.database import ActivityLog, Session
from controllers.auth_controller import AuthController

class BackupController:
    """متحكم النسخ الاحتياطي"""
    
    def __init__(self, auth_controller: AuthController):
        """
        تهيئة متحكم النسخ الاحتياطي
        
        Args:
            auth_controller: متحكم المصادقة
        """
        self.auth_controller = auth_controller
    
    def create_backup(self, description: str = "") -> str:
        """
        إنشاء نسخة احتياطية من قاعدة البيانات
        
        Args:
            description: وصف النسخة الاحتياطية
            
        Returns:
            مسار ملف النسخة الاحتياطية إذا نجحت العملية، نص فارغ خلاف ذلك
        """
        # التحقق من تسجيل الدخول وصلاحيات المدير
        if not self.auth_controller.is_authenticated() or not self.auth_controller.is_admin():
            return ""
        
        # إنشاء النسخة الاحتياطية
        backup_path = BackupManager.create_backup(description)
        
        # تسجيل نشاط إنشاء النسخة الاحتياطية
        if backup_path:
            session = Session()
            try:
                activity = ActivityLog(
                    action="إنشاء نسخة احتياطية",
                    details=f"تم إنشاء نسخة احتياطية: {os.path.basename(backup_path)}",
                    user_id=self.auth_controller.current_user.id
                )
                session.add(activity)
                session.commit()
            finally:
                session.close()
        
        return backup_path
    
    def restore_backup(self, backup_path: str) -> bool:
        """
        استعادة قاعدة البيانات من نسخة احتياطية
        
        Args:
            backup_path: مسار ملف النسخة الاحتياطية
            
        Returns:
            True إذا نجحت العملية، False خلاف ذلك
        """
        # التحقق من تسجيل الدخول وصلاحيات المدير
        if not self.auth_controller.is_authenticated() or not self.auth_controller.is_admin():
            return False
        
        # استعادة النسخة الاحتياطية
        success = BackupManager.restore_backup(backup_path)
        
        # تسجيل نشاط استعادة النسخة الاحتياطية
        if success:
            session = Session()
            try:
                activity = ActivityLog(
                    action="استعادة نسخة احتياطية",
                    details=f"تم استعادة النسخة الاحتياطية: {os.path.basename(backup_path)}",
                    user_id=self.auth_controller.current_user.id
                )
                session.add(activity)
                session.commit()
            finally:
                session.close()
        
        return success
    
    def get_backups(self) -> List[Dict[str, Any]]:
        """
        الحصول على قائمة النسخ الاحتياطية المتوفرة
        
        Returns:
            قائمة بمعلومات النسخ الاحتياطية
        """
        # التحقق من تسجيل الدخول
        if not self.auth_controller.is_authenticated():
            return []
        
        return BackupManager.get_backups()
    
    def delete_backup(self, backup_path: str) -> bool:
        """
        حذف نسخة احتياطية
        
        Args:
            backup_path: مسار ملف النسخة الاحتياطية
            
        Returns:
            True إذا نجحت العملية، False خلاف ذلك
        """
        # التحقق من تسجيل الدخول وصلاحيات المدير
        if not self.auth_controller.is_authenticated() or not self.auth_controller.is_admin():
            return False
        
        # حذف النسخة الاحتياطية
        success = BackupManager.delete_backup(backup_path)
        
        # تسجيل نشاط حذف النسخة الاحتياطية
        if success:
            session = Session()
            try:
                activity = ActivityLog(
                    action="حذف نسخة احتياطية",
                    details=f"تم حذف النسخة الاحتياطية: {os.path.basename(backup_path)}",
                    user_id=self.auth_controller.current_user.id
                )
                session.add(activity)
                session.commit()
            finally:
                session.close()
        
        return success
