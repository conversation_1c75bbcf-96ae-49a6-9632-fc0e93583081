أحتاج إلى برنامج سطح مكتب بلغة بايثون يعمل على نظام ويندوز 7 وما فوق لتدبير الحوادث المدرسية. البرنامج سيكون منصة متكاملة لتسجيل وإدارة ومتابعة الحوادث التي تقع في المديرية الإقليمية، وأيضا نظام متابعة ملفات الضمان المدرسي (مراسلات، الملفات الناقصة)، قم ببرمجة جميع النقط :

المتطلبات الأساسية للبرنامج:
- واجهة مستخدم رسومية سهلة الاستخدام باستخدام مكتبة مثل PyQt
- يعمل في ويندوز 7 أو 10 فما فوق (ضروري)
- قاعدة بيانات لتخزين معلومات الحوادث (يمكن استخدام SQLite للبساطة)
- نظام تسجيل دخول للمستخدمين مع صلاحيات مختلفة (مدير، مستخدم)، سجل نشاطات المستخدمين على النظام، إمكانية تغيير كلمة المرور وبيانات المستخدم
- هيكل منظم يعتمد نمط Model-View-Controller (MVC)

الوظائف المطلوبة:

- تسجيل حادثة جديدة تتضمن المعلومات التالية: اسم التلميذ، رقم مسار، الجنس (ذكر/أنثى)، المؤسسة، الوسط:(حضري/قروي)، السلك (ابتدائي، إعدادي، تأهيلي)، نوع الحادثة (حادثة حادثة مدرسية، حادثة تنقل، حادثة رياضية)، مرجع التأمين، تاريخ التسوية، الحالة (قيد التسوية، تمت التسوية، مرفوض، تم الدفع )، رقم الملف.
 * رقم ملف الحادثة: سيكون تلقائيا ولكن مع إمكانية تعديله، بحيث سيكون بهذا الشكل مثلا: الحوادث الخاصة بالابتدائي ستبدأ بحرف P ثم رقمين من السنة ثم أرقام تصاعدية P2501، أما الإعدادي سيكون بهذا الشكل: C2501، التأهيلي L2501، بحيث كل سنة يتم تجديد العداد وتغير الرقمين اللذان يأتين بعد الحرف وهما أرقم السنة.
*  البحث في الحوادث حسب معايير مختلفة (التاريخ، نوع الحادثة، اسم التلميذ، المؤسسة، السلك..)
- قائمة الحوادث: تتضمن إمكانية التعديل والتغير وخاصية البحث
- التقارير: إنشاء تقارير عن الحوادث (يومية، أسبوعية، شهرية، سنوية)، وحسب نوع الحادثة، الوسط، السلك، الجنس، الحال،  إمكانية تصدير التقارير إلى PDF أو Excel، قوالب جاهزة للتقارير (الإرساليات): توفير مجموعة متنوعة من القوالب الجاهزة للتقارير التي يمكن تخصيصها وتصديرها بسهولة، مع أرشيف لهذه الإرسالات، وان تكون مرتبطة بملف الحادثة
- النسخ الاحتياطية: إمكانية إنشاء واستعادة النسخ الاحتياطية في الجهاز أو في google drive
- الرئيسية: تتضمن إحصائيات حول الحوادث المدرسية والضمان المدرسي

أرجو أن يكون البرنامج قابلاً للتثبيت بسهولة على أجهزة ويندوز مع وجود دليل استخدام واضح. كما أرجو التأكد من أمان المعلومات وخصوصيتها نظراً لحساسية البيانات المتعلقة بالتلاميذ.
