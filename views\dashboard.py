"""
لوحة المعلومات الرئيسية
"""
import os
import sys
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame,
    QGridLayout, QScrollArea
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QColor, QPalette

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from controllers.incident_controller import IncidentController

class StatisticWidget(QFrame):
    """ويدجت إحصائية"""
    
    def __init__(self, title, value, description=None, color="#3498db"):
        """
        تهيئة ويدجت الإحصائية
        
        Args:
            title: عنوان الإحصائية
            value: قيمة الإحصائية
            description: وصف الإحصائية (اختياري)
            color: لون الإحصائية
        """
        super().__init__()
        
        # إعداد الإطار
        self.setObjectName("dashboardFrame")
        self.setFrameShape(QFrame.StyledPanel)
        self.setMinimumHeight(120)
        
        # تعيين لون الخلفية
        palette = self.palette()
        palette.setColor(QPalette.Window, QColor(color).lighter(180))
        self.setAutoFillBackground(True)
        self.setPalette(palette)
        
        # إنشاء التخطيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # عنوان الإحصائية
        title_label = QLabel(title)
        title_label.setObjectName("dashboardTitle")
        layout.addWidget(title_label)
        
        # قيمة الإحصائية
        value_label = QLabel(str(value))
        value_label.setObjectName("dashboardValue")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        # وصف الإحصائية (اختياري)
        if description:
            description_label = QLabel(description)
            description_label.setObjectName("dashboardDescription")
            description_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(description_label)

class DashboardWidget(QWidget):
    """ويدجت لوحة المعلومات"""
    
    def __init__(self, incident_controller: IncidentController):
        """
        تهيئة ويدجت لوحة المعلومات
        
        Args:
            incident_controller: متحكم الحوادث
        """
        super().__init__()
        
        self.incident_controller = incident_controller
        
        # إنشاء التخطيط
        self.setup_ui()
        
        # تحديث الإحصائيات
        self.update_statistics()
        
        # إعداد مؤقت لتحديث الإحصائيات كل 5 دقائق
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_statistics)
        self.update_timer.start(5 * 60 * 1000)  # 5 دقائق
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        # ويدجت المحتوى
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)
        
        # عنوان لوحة المعلومات
        title_label = QLabel("لوحة المعلومات")
        title_label.setObjectName("dashboardMainTitle")
        title_label.setAlignment(Qt.AlignCenter)
        content_layout.addWidget(title_label)
        
        # قسم الإحصائيات العامة
        general_stats_label = QLabel("الإحصائيات العامة")
        general_stats_label.setObjectName("dashboardSectionTitle")
        content_layout.addWidget(general_stats_label)
        
        # شبكة الإحصائيات العامة
        self.general_stats_grid = QGridLayout()
        self.general_stats_grid.setSpacing(10)
        content_layout.addLayout(self.general_stats_grid)
        
        # قسم إحصائيات المستوى التعليمي
        education_stats_label = QLabel("إحصائيات حسب المستوى التعليمي")
        education_stats_label.setObjectName("dashboardSectionTitle")
        content_layout.addWidget(education_stats_label)
        
        # شبكة إحصائيات المستوى التعليمي
        self.education_stats_grid = QGridLayout()
        self.education_stats_grid.setSpacing(10)
        content_layout.addLayout(self.education_stats_grid)
        
        # قسم إحصائيات نوع الحادثة
        incident_type_stats_label = QLabel("إحصائيات حسب نوع الحادثة")
        incident_type_stats_label.setObjectName("dashboardSectionTitle")
        content_layout.addWidget(incident_type_stats_label)
        
        # شبكة إحصائيات نوع الحادثة
        self.incident_type_stats_grid = QGridLayout()
        self.incident_type_stats_grid.setSpacing(10)
        content_layout.addLayout(self.incident_type_stats_grid)
        
        # قسم إحصائيات الحالة
        status_stats_label = QLabel("إحصائيات حسب الحالة")
        status_stats_label.setObjectName("dashboardSectionTitle")
        content_layout.addWidget(status_stats_label)
        
        # شبكة إحصائيات الحالة
        self.status_stats_grid = QGridLayout()
        self.status_stats_grid.setSpacing(10)
        content_layout.addLayout(self.status_stats_grid)
        
        # قسم إحصائيات أخرى
        other_stats_label = QLabel("إحصائيات أخرى")
        other_stats_label.setObjectName("dashboardSectionTitle")
        content_layout.addWidget(other_stats_label)
        
        # شبكة إحصائيات أخرى
        self.other_stats_grid = QGridLayout()
        self.other_stats_grid.setSpacing(10)
        content_layout.addLayout(self.other_stats_grid)
        
        # تعيين ويدجت المحتوى لمنطقة التمرير
        scroll_area.setWidget(content_widget)
        
        # إضافة منطقة التمرير إلى التخطيط الرئيسي
        main_layout.addWidget(scroll_area)
    
    def update_statistics(self):
        """تحديث الإحصائيات"""
        
        # الحصول على الإحصائيات من متحكم الحوادث
        stats = self.incident_controller.get_statistics()
        
        # مسح الإحصائيات الحالية
        self.clear_statistics()
        
        # إضافة الإحصائيات العامة
        self.add_general_statistics(stats)
        
        # إضافة إحصائيات المستوى التعليمي
        self.add_education_statistics(stats)
        
        # إضافة إحصائيات نوع الحادثة
        self.add_incident_type_statistics(stats)
        
        # إضافة إحصائيات الحالة
        self.add_status_statistics(stats)
        
        # إضافة إحصائيات أخرى
        self.add_other_statistics(stats)
    
    def clear_statistics(self):
        """مسح الإحصائيات الحالية"""
        
        # مسح شبكة الإحصائيات العامة
        self.clear_layout(self.general_stats_grid)
        
        # مسح شبكة إحصائيات المستوى التعليمي
        self.clear_layout(self.education_stats_grid)
        
        # مسح شبكة إحصائيات نوع الحادثة
        self.clear_layout(self.incident_type_stats_grid)
        
        # مسح شبكة إحصائيات الحالة
        self.clear_layout(self.status_stats_grid)
        
        # مسح شبكة إحصائيات أخرى
        self.clear_layout(self.other_stats_grid)
    
    def clear_layout(self, layout):
        """
        مسح التخطيط
        
        Args:
            layout: التخطيط المراد مسحه
        """
        if layout is not None:
            while layout.count():
                item = layout.takeAt(0)
                widget = item.widget()
                if widget is not None:
                    widget.deleteLater()
                else:
                    self.clear_layout(item.layout())
    
    def add_general_statistics(self, stats):
        """
        إضافة الإحصائيات العامة
        
        Args:
            stats: قاموس الإحصائيات
        """
        # إجمالي عدد الحوادث
        total_incidents = StatisticWidget(
            "إجمالي الحوادث",
            stats.get('total_incidents', 0),
            color="#3498db"
        )
        self.general_stats_grid.addWidget(total_incidents, 0, 0)
        
        # عدد الحوادث في الشهر الحالي
        current_month = StatisticWidget(
            "الشهر الحالي",
            stats.get('time_periods', {}).get('current_month', 0),
            color="#2ecc71"
        )
        self.general_stats_grid.addWidget(current_month, 0, 1)
        
        # عدد الحوادث في السنة الحالية
        current_year = StatisticWidget(
            "السنة الحالية",
            stats.get('time_periods', {}).get('current_year', 0),
            color="#9b59b6"
        )
        self.general_stats_grid.addWidget(current_year, 0, 2)
    
    def add_education_statistics(self, stats):
        """
        إضافة إحصائيات المستوى التعليمي
        
        Args:
            stats: قاموس الإحصائيات
        """
        # الابتدائي
        primary = StatisticWidget(
            "الابتدائي",
            stats.get('education_level', {}).get('primary', 0),
            color="#e74c3c"
        )
        self.education_stats_grid.addWidget(primary, 0, 0)
        
        # الإعدادي
        middle = StatisticWidget(
            "الإعدادي",
            stats.get('education_level', {}).get('middle', 0),
            color="#f39c12"
        )
        self.education_stats_grid.addWidget(middle, 0, 1)
        
        # التأهيلي
        high = StatisticWidget(
            "التأهيلي",
            stats.get('education_level', {}).get('high', 0),
            color="#1abc9c"
        )
        self.education_stats_grid.addWidget(high, 0, 2)
    
    def add_incident_type_statistics(self, stats):
        """
        إضافة إحصائيات نوع الحادثة
        
        Args:
            stats: قاموس الإحصائيات
        """
        # حادثة مدرسية
        school = StatisticWidget(
            "حادثة مدرسية",
            stats.get('incident_type', {}).get('school', 0),
            color="#3498db"
        )
        self.incident_type_stats_grid.addWidget(school, 0, 0)
        
        # حادثة تنقل
        transport = StatisticWidget(
            "حادثة تنقل",
            stats.get('incident_type', {}).get('transport', 0),
            color="#2ecc71"
        )
        self.incident_type_stats_grid.addWidget(transport, 0, 1)
        
        # حادثة رياضية
        sport = StatisticWidget(
            "حادثة رياضية",
            stats.get('incident_type', {}).get('sport', 0),
            color="#9b59b6"
        )
        self.incident_type_stats_grid.addWidget(sport, 0, 2)
    
    def add_status_statistics(self, stats):
        """
        إضافة إحصائيات الحالة
        
        Args:
            stats: قاموس الإحصائيات
        """
        # قيد التسوية
        pending = StatisticWidget(
            "قيد التسوية",
            stats.get('status', {}).get('pending', 0),
            color="#f39c12"
        )
        self.status_stats_grid.addWidget(pending, 0, 0)
        
        # تمت التسوية
        resolved = StatisticWidget(
            "تمت التسوية",
            stats.get('status', {}).get('resolved', 0),
            color="#2ecc71"
        )
        self.status_stats_grid.addWidget(resolved, 0, 1)
        
        # مرفوض
        rejected = StatisticWidget(
            "مرفوض",
            stats.get('status', {}).get('rejected', 0),
            color="#e74c3c"
        )
        self.status_stats_grid.addWidget(rejected, 0, 2)
        
        # تم الدفع
        paid = StatisticWidget(
            "تم الدفع",
            stats.get('status', {}).get('paid', 0),
            color="#3498db"
        )
        self.status_stats_grid.addWidget(paid, 0, 3)
    
    def add_other_statistics(self, stats):
        """
        إضافة إحصائيات أخرى
        
        Args:
            stats: قاموس الإحصائيات
        """
        # ذكور
        male = StatisticWidget(
            "ذكور",
            stats.get('gender', {}).get('male', 0),
            color="#3498db"
        )
        self.other_stats_grid.addWidget(male, 0, 0)
        
        # إناث
        female = StatisticWidget(
            "إناث",
            stats.get('gender', {}).get('female', 0),
            color="#e84393"
        )
        self.other_stats_grid.addWidget(female, 0, 1)
        
        # حضري
        urban = StatisticWidget(
            "حضري",
            stats.get('environment', {}).get('urban', 0),
            color="#2ecc71"
        )
        self.other_stats_grid.addWidget(urban, 0, 2)
        
        # قروي
        rural = StatisticWidget(
            "قروي",
            stats.get('environment', {}).get('rural', 0),
            color="#f39c12"
        )
        self.other_stats_grid.addWidget(rural, 0, 3)
