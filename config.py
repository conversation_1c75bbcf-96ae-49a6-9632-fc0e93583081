"""
ملف إعدادات التطبيق
"""
import os
import sys
from pathlib import Path

# معلومات التطبيق
APP_NAME = "نظام إدارة الحوادث المدرسية"
APP_VERSION = "1.0.0"
APP_AUTHOR = "Daman5"

# مسارات المشروع
if getattr(sys, 'frozen', False):
    # إذا كان التطبيق مجمّع (exe)
    BASE_DIR = Path(sys.executable).parent
else:
    # إذا كان التطبيق في وضع التطوير
    BASE_DIR = Path(__file__).parent

# مسار قاعدة البيانات
DB_PATH = os.path.join(BASE_DIR, "data", "database.db")
DB_URI = f"sqlite:///{DB_PATH}"

# مسار النسخ الاحتياطية
BACKUP_DIR = os.path.join(BASE_DIR, "data", "backups")

# إعدادات واجهة المستخدم
UI_THEME = "light"  # light أو dark
UI_FONT_FAMILY = "Arial"
UI_FONT_SIZE = 10

# إعدادات التقارير
REPORTS_DIR = os.path.join(BASE_DIR, "data", "reports")
TEMPLATES_DIR = os.path.join(BASE_DIR, "resources", "templates")

# إعدادات السجلات
LOG_DIR = os.path.join(BASE_DIR, "data", "logs")
LOG_LEVEL = "INFO"

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [os.path.dirname(DB_PATH), BACKUP_DIR, REPORTS_DIR, LOG_DIR]:
    os.makedirs(directory, exist_ok=True)
