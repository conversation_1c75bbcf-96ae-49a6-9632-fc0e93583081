"""
دوال مساعدة للتطبيق
"""
import bcrypt
import datetime
import os
import sys
from sqlalchemy.orm import Session

# إضافة المسار الرئيسي للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import EducationLevel, Setting

def hash_password(password: str) -> str:
    """
    تشفير كلمة المرور باستخدام bcrypt
    
    Args:
        password: كلمة المرور النصية
        
    Returns:
        كلمة المرور المشفرة
    """
    # تحويل كلمة المرور إلى بايت
    password_bytes = password.encode('utf-8')
    # إنشاء الملح وتشفير كلمة المرور
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password_bytes, salt)
    # إرجاع كلمة المرور المشفرة كنص
    return hashed.decode('utf-8')

def verify_password(stored_password: str, provided_password: str) -> bool:
    """
    التحقق من صحة كلمة المرور
    
    Args:
        stored_password: كلمة المرور المخزنة (المشفرة)
        provided_password: كلمة المرور المدخلة للتحقق
        
    Returns:
        True إذا كانت كلمة المرور صحيحة، False خلاف ذلك
    """
    # تحويل كلمات المرور إلى بايت
    stored_password_bytes = stored_password.encode('utf-8')
    provided_password_bytes = provided_password.encode('utf-8')
    # التحقق من صحة كلمة المرور
    return bcrypt.checkpw(provided_password_bytes, stored_password_bytes)

def generate_file_number(session: Session, education_level: EducationLevel) -> str:
    """
    إنشاء رقم ملف جديد للحادثة
    
    Args:
        session: جلسة قاعدة البيانات
        education_level: المستوى التعليمي (ابتدائي، إعدادي، تأهيلي)
        
    Returns:
        رقم الملف الجديد
    """
    # الحصول على السنة الحالية
    year_setting = session.query(Setting).filter_by(key='current_year').first()
    current_year = year_setting.value
    
    # تحديد المفتاح والحرف الأول حسب المستوى التعليمي
    if education_level == EducationLevel.PRIMARY:
        counter_key = 'primary_counter'
        prefix = 'P'
    elif education_level == EducationLevel.MIDDLE:
        counter_key = 'middle_counter'
        prefix = 'C'
    elif education_level == EducationLevel.HIGH:
        counter_key = 'high_counter'
        prefix = 'L'
    else:
        raise ValueError("مستوى تعليمي غير صالح")
    
    # الحصول على العداد الحالي وزيادته
    counter_setting = session.query(Setting).filter_by(key=counter_key).first()
    current_counter = int(counter_setting.value)
    new_counter = current_counter + 1
    
    # تحديث العداد في قاعدة البيانات
    counter_setting.value = str(new_counter)
    session.commit()
    
    # إنشاء رقم الملف
    file_number = f"{prefix}{current_year}{new_counter:03d}"
    
    return file_number

def check_new_year(session: Session) -> bool:
    """
    التحقق مما إذا كانت السنة الحالية جديدة وتحديث الإعدادات إذا لزم الأمر
    
    Args:
        session: جلسة قاعدة البيانات
        
    Returns:
        True إذا تم تحديث السنة، False خلاف ذلك
    """
    # الحصول على السنة الحالية من الإعدادات
    year_setting = session.query(Setting).filter_by(key='current_year').first()
    stored_year = int(year_setting.value)
    
    # الحصول على السنة الحالية من النظام
    current_year = datetime.datetime.now().year % 100
    
    # التحقق مما إذا كانت السنة قد تغيرت
    if current_year != stored_year:
        # تحديث السنة
        year_setting.value = str(current_year)
        
        # إعادة تعيين العدادات
        for counter_key in ['primary_counter', 'middle_counter', 'high_counter']:
            counter_setting = session.query(Setting).filter_by(key=counter_key).first()
            counter_setting.value = '0'
        
        # حفظ التغييرات
        session.commit()
        return True
    
    return False

def format_date(date: datetime.datetime) -> str:
    """
    تنسيق التاريخ بالشكل المناسب
    
    Args:
        date: كائن التاريخ
        
    Returns:
        التاريخ المنسق كنص
    """
    if date is None:
        return ""
    
    return date.strftime("%Y/%m/%d")

def parse_date(date_str: str) -> datetime.datetime:
    """
    تحويل النص إلى كائن تاريخ
    
    Args:
        date_str: نص التاريخ
        
    Returns:
        كائن التاريخ
    """
    if not date_str:
        return None
    
    try:
        return datetime.datetime.strptime(date_str, "%Y/%m/%d")
    except ValueError:
        try:
            return datetime.datetime.strptime(date_str, "%Y-%m-%d")
        except ValueError:
            return None
